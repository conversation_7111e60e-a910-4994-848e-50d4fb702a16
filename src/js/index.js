// Галерея и лайтбоксы от Fancybox
import { Fancybox } from '@fancyapps/ui';
import '@fancyapps/ui/dist/fancybox/fancybox.css';

Fancybox.bind('[data-fancybox]', {
	// Your custom options
});

// Мобильная навигация удалена

// Единая кнопка меню
const mainMenuBtn = document.getElementById('mainMenuBtn');
const mainMenuModal = document.getElementById('mainMenuModal');

console.log('Menu elements:', { mainMenuBtn, mainMenuModal });

// Функция для блокировки/разблокировки скролла
function toggleBodyScroll(disable) {
    if (disable) {
        document.body.style.overflow = 'hidden';
    } else {
        document.body.style.overflow = '';
    }
}

// Функция для закрытия меню
function closeMenu() {
    mainMenuModal?.classList.remove('active');
    mainMenuBtn?.classList.remove('active');

    // Разблокируем скролл
    toggleBodyScroll(false);
}

// Обработчик для единой кнопки меню
if (mainMenuBtn && mainMenuModal) {
    mainMenuBtn.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();

        console.log('Main menu button clicked!');

        const isActive = mainMenuModal.classList.contains('active');

        if (isActive) {
            closeMenu();
        } else {
            mainMenuModal.classList.add('active');
            mainMenuBtn.classList.add('active');

            // Блокируем скролл
            toggleBodyScroll(true);
        }
    });
}

// Закрытие меню при клике вне меню (на документе)
document.addEventListener('click', function(e) {
    // Проверяем, что клик был не по кнопке меню и не внутри modal
    if (!mainMenuBtn?.contains(e.target) && !mainMenuModal?.contains(e.target)) {
        closeMenu();
    }
});

// Закрытие меню при нажатии Escape
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeMenu();
    }
});