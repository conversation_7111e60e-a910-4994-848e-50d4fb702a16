.header {
	position: relative; // Для позиционирования выпадающих меню
}

.main-header__section {
	position: relative; // Для позиционирования выпадающих меню относительно этой секции
}

.header__nav {
	@include tablet {
		display: none;
	}
}
.sub-header__section {
	border-bottom: .1rem solid var(--borders-color);
}
.sub-header {
	text-transform: uppercase;
	font-weight: var(--font-weight-semibold);
	font-size: 1.3rem;
	color: var(--text-color);
	padding: 1rem 0;
	

	& a {
		color: var(--text-color);
		transition: color 0.3s ease;
		cursor: pointer;
	}

	& a:hover {
		color: var(--accent-color);

		.sub-header__icon-svg {
			color: var(--accent-color);
		}
	}
}

.sub-header__socials {
	gap: 2rem;

	@media (max-width:900px) {
		gap: .5rem 2rem;
	}
	@media (min-width:1200px) {
		gap: 4rem;
	}
}

.sub-header__calculate-phone {
	gap: 2rem;
	justify-content: end;

	@media (max-width:900px) {
		gap: .5rem 2rem;
	}
	@media (min-width:1200px) {
		gap: 4rem;
	}
}

.sub-header__item {
	display: inline-flex;
	align-items: center;
	gap: 0.8rem;
	color: var(--text-color);
	transition: color 0.3s ease;

	&:hover {
		color: var(--accent-color);

		.sub-header__item-svg {
			color: var(--accent-color);
		}
	}
}

.sub-header__item-svg {
	width: 2rem;
	height: 2rem;
	color: var(--text-color);
	transition: color 0.3s ease;
	flex-shrink: 0;

	@media (max-width:460px) {
		display:none;
	}
}

@media (max-width:420px) {
	.sub-header__item-email,
	.sub-header__item-calculate {
		display: none;
	}
}

.phone {
	font-weight: var(--font-weight-bold);
}
.main-header__section {
	border-bottom: .1rem solid var(--borders-color);
}
.main-header {
	align-items: center;
	font-size: 1.3rem;
	color: var(--text-color);
	padding: 1rem 0;

}
.logo-descriptor {
	gap:3rem;

	@media (max-width:576px) {
		gap:5.5rem;
	}
}
.logo {
	width: 17.2rem;
	height: 8.6rem;
}
.descriptor {
	text-transform: none;
	font-weight: var(--font-weight-regular);
	max-width: 11rem;
	font-size:1.3rem;
	color: var(--grey-text);
	padding-top: .7rem;
	margin-right: 2rem;

	// Скрываем дескриптор на мобильных устройствах (<768px)
	@media (max-width: 767px) {
		display: none;
	}
}
// Основной контейнер меню
.main-menu {
	position: relative;

	.container {
		display: flex;
		align-items: center;
		gap: 5rem;

		@media (max-width: 1310px) {
			gap: 2rem;
		}
	}

	// Список меню (показывается только на десктопе ≥1280px)
	&__list {
		display: flex;
		gap: 5rem;

		@media (max-width: 1279px) {
			display: none;
		}

		@media (max-width: 1310px) {
			gap: 2rem;
		}

		li a {
			color: var(--black-text);
			text-decoration: none;
			font-size: 1.6rem;
			font-weight: var(--font-weight-medium);
			transition: color 0.3s ease;

			&:hover {
				color: var(--accent-color);
			}
		}
	}
}
// Единая кнопка меню (адаптивная)
.main-menu-btn {
	text-transform: uppercase;
	font-weight: var(--font-weight-semibold);
	background: var(--alternate-accent-color);
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: center;
	color: var(--white-text);
	border-radius: 7px;
	padding: 1.3rem 1.2rem;
	width: 17rem;
	height: 5rem;
	gap: .8rem;
	flex-wrap: nowrap;
	white-space: nowrap;
	position: relative;
	overflow: hidden;
	flex-shrink: 0;
	border: none;
	cursor: pointer;
	transition: all 0.3s ease;

	// Адаптивные стили для планшетов (768-1279px) - квадратная кнопка
@media (min-width: 768px) and (max-width: 1279px) {
	width: 6rem;
	height: 6rem;
	flex-direction: column;
	background: var(--light-bg, #f8f9fa);
	color: var(--black-text);
	gap: 0.5rem;
	padding: 1rem;
}

// Адаптивные стили для мобильных (<768px) - обычная кнопка
@media (max-width: 767px) {
	width: auto;
	height: auto;
	flex-direction: row;
	background: var(--accent-color);
	color: var(--white-text);
	gap: .8rem;
	padding: 1.3rem 1.2rem;
}

&::before {
	content: '';
	position: absolute;
	top: 0;
	left: -100%;
	width: 50%;
	height: 100%;
	background: linear-gradient(
		90deg,
		transparent,
		rgba(255, 255, 255, 0.4),
		transparent
	);
	transform: skewX(-30deg);
	animation: shine 7s infinite;
	pointer-events: none;
}

	// Стили для иконок
	.menu-btn__icon {
		width: 2.4rem;
		height: 2.4rem;
		transition: all 0.3s ease;

		// Показываем разные иконки в зависимости от экрана
		&--catalog {
			// Показываем только на десктопе ≥1280px
			@media (max-width: 1279px) {
				display: none;
			}
		}

		&--burger {
			// Показываем только на планшетах и мобильных <1280px
			@media (min-width: 1280px) {
				display: none;
			}
		}

		// Скрываем иконку при активном состоянии
		.main-menu-btn.active & {
			transform: rotate(90deg) scale(0);
		}
	}

	.menu-btn__close {
		width: 2.4rem;
		height: 2.4rem;
		position: absolute;
		top: 50%;
		left: 1.4rem;
		transform: translateY(-50%) rotate(-90deg) scale(0);
		transition: all 0.3s ease;

		// Показываем крестик при активном состоянии
		.main-menu-btn.active & {
			transform: translateY(-50%) rotate(0deg) scale(1);
		}
	}

	// Стили для текста
	.menu-btn__text {
		font-size: 1.4rem;
		transition: all 0.3s ease;

		&--catalog {
			// Показываем только на десктопе ≥1280px
			@media (max-width: 1279px) {
				display: none;
			}
		}

		&--menu {
			// Показываем только на планшетах (768-1279px)
			@media (min-width: 1280px), (max-width: 767px) {
				display: none;
			}

			font-size: 1.2rem;
			font-weight: var(--font-weight-bold);
		}
	}

	&:hover {
		background: linear-gradient(180deg, #3cb5f1 0%, #15a0e5 100%);

		@media (min-width: 768px) and (max-width: 1279px) {
			background: var(--accent-color);
			color: var(--white-text);
		}
	}

	@media (max-width: 380px) {
		padding: 1.3rem 1rem;
		width: 15rem;
		height: 4rem;
		gap: .5rem;
		font-size: 1.2rem;
	}
	
}
@keyframes shine {
    0% { left: -100%; }
    20% { left: 100%; }  // Пролетает за 0.7 сек
    100% { left: 100%; }
}

// Выпадающее меню
.main-menu-modal {
	position: absolute;
	top: 100%;
	left: 0;
	right: 0;
	background: var(--white-bg);
	border: 1px solid var(--borders-color);
	border-radius: 0 0 1rem 1rem;
	box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
	max-height: 60vh;
	overflow-y: auto;
	z-index: 1000;
	opacity: 0;
	visibility: hidden;
	transform: translateY(-1rem);
	transition: all 0.3s ease;

	&.active {
		opacity: 1;
		visibility: visible;
		transform: translateY(0);
	}

	.container {
		padding: 3rem 2rem;
	}
}

// Секции меню
.main-menu__section {
	margin-bottom: 4rem;

	&:last-child {
		margin-bottom: 0;
	}

	h3 {
		font-size: 2.4rem;
		font-weight: var(--font-weight-bold);
		color: var(--black-text);
		margin-bottom: 2rem;
		padding-bottom: 1rem;
		border-bottom: 2px solid var(--accent-color);
	}

	// Скрываем разделы сайта на десктопе ≥1280px (там они уже видны в хедере)
	&.main-menu__sections {
		@media (min-width: 1280px) {
			display: none;
		}
	}
}

// Навигационный список
.main-menu__nav-list {
	display: flex;
	flex-direction: column;
	gap: 1.5rem;

	li a {
		display: block;
		padding: 1.2rem 0;
		color: var(--black-text);
		text-decoration: none;
		font-size: 1.6rem;
		font-weight: var(--font-weight-medium);
		border-bottom: 1px solid var(--borders-color);
		transition: all 0.3s ease;

		&:hover {
			color: var(--accent-color);
			padding-left: 1rem;
		}
	}
}

// Каталог услуг
.catalog-menu__content {
	padding: 2rem 0;
	color: var(--text-color);
	font-size: 1.6rem;

	p {
		color: var(--grey-text);
		font-style: italic;
	}
}

@media (max-width:1400px) {
	.hide-on-1400 {
		display:none;
	}
}
