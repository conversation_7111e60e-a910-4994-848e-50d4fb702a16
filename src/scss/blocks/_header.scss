.header {
}

.header__nav {
	@include tablet {
		display: none;
	}
}
.sub-header__section {
	border-bottom: .1rem solid var(--borders-color);
}
.sub-header {
	text-transform: uppercase;
	font-weight: var(--font-weight-semibold);
	font-size: 1.3rem;
	color: var(--text-color);
	padding: 1rem 0;
	

	& a {
		color: var(--text-color);
		transition: color 0.3s ease;
		cursor: pointer;
	}

	& a:hover {
		color: var(--accent-color);

		.sub-header__icon-svg {
			color: var(--accent-color);
		}
	}
}

.sub-header__socials {
	gap: 2rem;

	@media (max-width:900px) {
		gap: .5rem 2rem;
	}
	@media (min-width:1200px) {
		gap: 4rem;
	}
}

.sub-header__calculate-phone {
	gap: 2rem;
	justify-content: end;

	@media (max-width:900px) {
		gap: .5rem 2rem;
	}
	@media (min-width:1200px) {
		gap: 4rem;
	}
}

.sub-header__item {
	display: inline-flex;
	align-items: center;
	gap: 0.8rem;
	color: var(--text-color);
	transition: color 0.3s ease;

	&:hover {
		color: var(--accent-color);

		.sub-header__item-svg {
			color: var(--accent-color);
		}
	}
}

.sub-header__item-svg {
	width: 2rem;
	height: 2rem;
	color: var(--text-color);
	transition: color 0.3s ease;
	flex-shrink: 0;

	@media (max-width:460px) {
		display:none;
	}
}

@media (max-width:420px) {
	.sub-header__item-email,
	.sub-header__item-calculate {
		display: none;
	}
}

.phone {
	font-weight: var(--font-weight-bold);
}
.main-header__section {
	border-bottom: .1rem solid var(--borders-color);
}
.main-header {
	align-items: center;
	font-size: 1.3rem;
	color: var(--text-color);
	padding: 1rem 0;
}
.logo-descriptor {
	gap:3rem;
}
.logo {
	width: 17.2rem;
	height: 8.6rem;
}
.descriptor {
	text-transform: none;
	font-weight: var(--font-weight-regular);
	max-width: 11rem;
	font-size:1.3rem;
	color: var(--grey-text);
	padding-top: .7rem;
	margin-right: 2rem;;
}
.main-menu {
	gap:5rem;

	@media (max-width:1310px) {
		gap:2rem;
	}
}
.menu-btn {
	text-transform: uppercase;
	font-weight: var(--font-weight-semibold);
	background: var(--alternate-accent-color);
	display:flex;
	flex-direction: row;
	align-items: center;
	justify-content: center;
	color: var(--white-text);
	border-radius: 7px;
	padding: 1.3rem 1.2rem;
	width: 17rem;
	height: 5rem;
	gap:.8rem;
	flex-wrap: nowrap;
	white-space: nowrap;
	position: relative;
    overflow: hidden;
	flex-shrink: 0;

	&::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;  // Начальная позиция за пределами кнопки
        width: 50%;   // Ширина блика
        height: 100%;
        background: linear-gradient(
            90deg,
            transparent,
            rgba(255, 255, 255, 0.4),  
            transparent
        );
        transform: skewX(-30deg);  // Искажение под 30 градусов
        animation: shine 7s infinite;  // Анимация каждые 7 секунд
        pointer-events: none;  // Блик не мешает кликам
    }

	

	&:hover {
		background: linear-gradient(180deg, #3cb5f1 0%, #15a0e5 100%);
	}

	&__icon {
        transition: transform 0.3s ease;
        
        // Скрываем иконку при активном состоянии
        .menu-btn--active & {
            transform: rotate(90deg) scale(0);
        }
    }
	// Крестик (изначально скрыт)
    &__close {
        position: absolute;
        top: 50%;
        left: 1.7rem;  // Позиционируем на месте обычной иконки
        transform: translateY(-50%) rotate(-90deg) scale(0);
        transition: transform 0.3s ease;
        width: 2.4rem;
        height: 2.4rem;
        flex-shrink: 0;

        // Показываем крестик при активном состоянии
        .menu-btn--active & {
            transform: translateY(-50%) rotate(0deg) scale(1);
        }
    }
	
}
@keyframes shine {
    0% { left: -100%; }
    20% { left: 100%; }  // Пролетает за 0.7 сек
    100% { left: 100%; }
}
.main-menu__list {
	font-size:1.5rem;
	gap:3rem;
	text-transform: uppercase;
	font-weight: var(--font-weight-semibold);
	// white-space: nowrap;

	& a {
		color: var(--black-text);
		transition: all 0.3s ease;

		&:hover {
			color: var(--accent-color);
			border-bottom:.2rem solid var(--accent-color);
			padding-bottom:.5rem;
		}
	}
	@media (min-width:963px) and (max-width:1449px) {
		gap:2rem;
	}
	@media (min-width:1550px) and (max-width:1700px) {
		gap:5rem;
	}
	@media (min-width:1701px) and (max-width:1920px) {
		gap:6rem;
	}
	@media (min-width:1921px) {
		gap:10rem;
	}

	// Скрываем список меню на экранах < 1280px
	@media (max-width: 1279px) {
		display: none;
	}
}
// Кнопка "Разделы сайта"
.menu-btn--sections {
	// Скрываем на больших экранах
	@media (min-width: 1280px) {
		display: none;
	}

	// Меняем цвет фона на accent-color
	background: var(--accent-color);

	&:hover {
		background: linear-gradient(180deg, #3cb5f1 0%, #15a0e5 100%);
	}
}

// Выпадающие меню
.menu-overlay {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100vh;
	background: rgba(0, 0, 0, 0.5);
	z-index: 1000;
	opacity: 0;
	visibility: hidden;
	transition: all 0.3s ease;

	&.active {
		opacity: 1;
		visibility: visible;
	}

	&__content {
		background: var(--white-bg);
		padding: 2rem 0;
		margin-top: var(--header-height, 10rem); // Отступ от header
		box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
	}
}

// Стили для каталога услуг
.catalog-menu {
	h3 {
		font-size: 2.4rem;
		font-weight: var(--font-weight-bold);
		color: var(--black-text);
		margin-bottom: 2rem;
	}

	&__list {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
		gap: 1.5rem;

		li a {
			display: block;
			padding: 1.5rem;
			background: var(--light-bg, #f8f9fa);
			border-radius: 8px;
			color: var(--black-text);
			text-decoration: none;
			font-weight: var(--font-weight-medium);
			transition: all 0.3s ease;

			&:hover {
				background: var(--accent-color);
				color: var(--white-text);
				transform: translateY(-2px);
			}
		}
	}
}

// Стили для разделов сайта
.sections-menu {
	h3 {
		font-size: 2.4rem;
		font-weight: var(--font-weight-bold);
		color: var(--black-text);
		margin-bottom: 2rem;
	}

	&__list {
		display: flex;
		flex-direction: column;
		gap: 1rem;

		li a {
			display: block;
			padding: 1.2rem 0;
			color: var(--black-text);
			text-decoration: none;
			font-size: 1.8rem;
			font-weight: var(--font-weight-medium);
			border-bottom: 1px solid var(--border-color, #e0e0e0);
			transition: all 0.3s ease;

			&:hover {
				color: var(--accent-color);
				padding-left: 1rem;
			}
		}
	}
}

@media (max-width:1400px) {
	.hide-on-1400 {
		display:none;
	}
}
