.header {
	position: relative; // Для позиционирования выпадающих меню
}

.main-header__section {
	position: relative; // Для позиционирования выпадающих меню относительно этой секции
}

.header__nav {
	@include tablet {
		display: none;
	}
}
.sub-header__section {
	border-bottom: .1rem solid var(--borders-color);
}
.sub-header {
	text-transform: uppercase;
	font-weight: var(--font-weight-semibold);
	font-size: 1.3rem;
	color: var(--text-color);
	padding: 1rem 0;
	

	& a {
		color: var(--text-color);
		transition: color 0.3s ease;
		cursor: pointer;
	}

	& a:hover {
		color: var(--accent-color);

		.sub-header__icon-svg {
			color: var(--accent-color);
		}
	}
}

.sub-header__socials {
	gap: 2rem;

	@media (max-width:900px) {
		gap: .5rem 2rem;
	}
	@media (min-width:1200px) {
		gap: 4rem;
	}
}

.sub-header__calculate-phone {
	gap: 2rem;
	justify-content: end;

	@media (max-width:900px) {
		gap: .5rem 2rem;
	}
	@media (min-width:1200px) {
		gap: 4rem;
	}
}

.sub-header__item {
	display: inline-flex;
	align-items: center;
	gap: 0.8rem;
	color: var(--text-color);
	transition: color 0.3s ease;

	&:hover {
		color: var(--accent-color);

		.sub-header__item-svg {
			color: var(--accent-color);
		}
	}
}

.sub-header__item-svg {
	width: 2rem;
	height: 2rem;
	color: var(--text-color);
	transition: color 0.3s ease;
	flex-shrink: 0;

	@media (max-width:460px) {
		display:none;
	}
}

@media (max-width:420px) {
	.sub-header__item-email,
	.sub-header__item-calculate {
		display: none;
	}
}

.phone {
	font-weight: var(--font-weight-bold);
}
.main-header__section {
	border-bottom: .1rem solid var(--borders-color);
}
.main-header {
	align-items: center;
	font-size: 1.3rem;
	color: var(--text-color);
	padding: 1rem 0;

}
.logo-descriptor {
	gap:3rem;

	@media (max-width:576px) {
		gap:5.5rem;
	}
}
.logo {
	width: 17.2rem;
	height: 8.6rem;
}
.descriptor {
	text-transform: none;
	font-weight: var(--font-weight-regular);
	max-width: 11rem;
	font-size:1.3rem;
	color: var(--grey-text);
	padding-top: .7rem;
	margin-right: 2rem;

	// Скрываем дескриптор на мобильных устройствах (<768px)
	@media (max-width: 767px) {
		display: none;
	}
}
.main-menu {
	gap:5rem;

	@media (max-width:719px) {
		margin-top:2rem;
	}
	@media (max-width:1310px) {
		gap:2rem;
	}
}
.menu-btn {
	text-transform: uppercase;
	font-weight: var(--font-weight-semibold);
	background: var(--alternate-accent-color);
	display:flex;
	flex-direction: row;
	align-items: center;
	justify-content: center;
	color: var(--white-text);
	border-radius: 7px;
	padding: 1.3rem 1.2rem;
	width: 17rem;
	height: 5rem;
	gap:.8rem;
	flex-wrap: nowrap;
	white-space: nowrap;
	position: relative;
    overflow: hidden;
	flex-shrink: 0;

	&::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;  // Начальная позиция за пределами кнопки
        width: 50%;   // Ширина блика
        height: 100%;
        background: linear-gradient(
            90deg,
            transparent,
            rgba(255, 255, 255, 0.4),  
            transparent
        );
        transform: skewX(-30deg);  // Искажение под 30 градусов
        animation: shine 7s infinite;  // Анимация каждые 7 секунд
        pointer-events: none;  // Блик не мешает кликам
    }

	

	&:hover {
		background: linear-gradient(180deg, #3cb5f1 0%, #15a0e5 100%);
	}

	&__icon {
        transition: transform 0.3s ease;
        
        // Скрываем иконку при активном состоянии
        .menu-btn--active & {
            transform: rotate(90deg) scale(0);
        }
		
		@media (max-width:380px) {
			width: 2rem;
		}
    }
	// Крестик (изначально скрыт)
    &__close {
        position: absolute;
        top: 50%;
        left: 1.4rem;  // Позиционируем на месте обычной иконки
        transform: translateY(-50%) rotate(-90deg) scale(0);
        transition: transform 0.3s ease;
        width: 2.4rem;
        height: 2.4rem;
        flex-shrink: 0;

        // Показываем крестик при активном состоянии
        .menu-btn--active & {
            transform: translateY(-50%) rotate(0deg) scale(1);
        }
    }

	@media (max-width:380px) {
		padding: 1.3rem 1rem;
		width: 15rem;
		height: 4rem;
		gap:.5rem;
		font-size: 1.2rem;
	}
	
}
@keyframes shine {
    0% { left: -100%; }
    20% { left: 100%; }  // Пролетает за 0.7 сек
    100% { left: 100%; }
}
.main-menu__list {
	font-size:1.5rem;
	gap:3rem;
	text-transform: uppercase;
	font-weight: var(--font-weight-semibold);
	
	// white-space: nowrap;

	& a {
		color: var(--black-text);
		border-bottom:.2rem solid transparent;
		padding-bottom:.5rem;
		transition: all 0.3s ease;

		&:hover {
			color: var(--accent-color);
			border-bottom:.2rem solid var(--accent-color);
			padding-bottom:.5rem;
		}
	}
	@media (min-width:963px) and (max-width:1549px) {
		gap:1.6rem;
	}
	@media (min-width:1550px) and (max-width:1700px) {
		gap:4rem;
	}
	@media (min-width:1701px) and (max-width:1920px) {
		gap:5.5rem;
	}
	@media (min-width:1921px) {
		gap:10rem;
	}

	// Скрываем список меню на экранах < 1280px
	@media (max-width: 1279px) {
		display: none;
	}
}
// Кнопка "Каталог услуг" (только для десктопа)
.menu-btn--catalog {
	// Показываем только на десктопе ≥1280px
	@media (max-width: 1279px) {
		display: none;
	}
}

// Универсальная кнопка меню (для планшетов и мобильных)
.menu-btn--universal {
	// Скрываем на больших экранах
	@media (min-width: 1280px) {
		display: none;
	}

	// Квадратная форма
	width: 6rem;
	height: 6rem;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	gap: 0.5rem;
	padding: 1rem;

	// Стили для планшетов (768-1279px) - квадратная кнопка
	@media (min-width: 768px) and (max-width: 1279px) {
		background: var(--light-bg, #f8f9fa);
		border: 1px solid var(--borders-color);

		&:hover {
			background: var(--accent-color);
			color: var(--white-text);
		}
	}

	// Стили для мобильных (<768px) - обычная кнопка
	@media (max-width: 767px) {
		width: auto;
		height: auto;
		flex-direction: row;
		gap: 1rem;
		padding: 1.2rem 2rem;
		background: var(--accent-color);
		color: var(--white-text);

		&:hover {
			background: linear-gradient(180deg, #ffaa78 0%, #fd823b 100%);
		}
	}

	&__burger {
		position: relative;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	&__text {
		font-size: 1rem;
		font-weight: var(--font-weight-bold);
		text-transform: uppercase;
		letter-spacing: 0.1em;

		// На мобильных скрываем текст "МЕНЮ"
		@media (max-width: 767px) {
			display: none;
		}
	}
}

// Выпадающие меню
.menu-overlay {
	position: absolute;
	top: 100%; // Позиционируем под main-header__section
	left: 0;
	width: 100%;
	background: var(--white-bg, #ffffff);
	z-index: 1000;
	opacity: 0;
	visibility: hidden;
	transform: translateY(-10px);
	transition: all 0.3s ease;
	box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);

	// Ограничиваем высоту и добавляем скролл для длинных меню
	max-height: 60vh;
	overflow-y: auto;

	&.active {
		opacity: 1;
		visibility: visible;
		transform: translateY(0);
	}

	&__content {
		padding: 3rem 0;
	}

	// Меню каталога (только для десктопа)
	&--catalog {
		@media (max-width: 1279px) {
			display: none;
		}
	}

	// Универсальное меню (только для планшетов и мобильных)
	&--universal {
		@media (min-width: 1280px) {
			display: none;
		}
	}
}

// Стили для каталога услуг
.catalog-menu {
	h3 {
		font-size: 2.4rem;
		font-weight: var(--font-weight-bold);
		color: var(--black-text);
		margin-bottom: 2rem;
	}

	&__content {
		padding: 2rem 0;
		color: var(--text-color);
		font-size: 1.6rem;

		// Здесь будут стили для многоуровневого меню WordPress
		// Пока временное содержимое
		p {
			color: var(--grey-text);
			font-style: italic;
		}
	}
}

// Стили для универсального меню
.universal-menu {
	&__section {
		margin-bottom: 4rem;

		&:last-child {
			margin-bottom: 0;
		}

		h3 {
			font-size: 2.4rem;
			font-weight: var(--font-weight-bold);
			color: var(--black-text);
			margin-bottom: 2rem;
			padding-bottom: 1rem;
			border-bottom: 2px solid var(--accent-color);
		}
	}

	&__list {
		display: flex;
		flex-direction: column;
		gap: 0;

		li a {
			display: block;
			padding: 1.5rem 0;
			color: var(--black-text);
			text-decoration: none;
			font-size: 1.6rem;
			font-weight: var(--font-weight-medium);
			border-bottom: 1px solid var(--borders-color);
			transition: all 0.3s ease;

			&:hover {
				color: var(--accent-color);
				padding-left: 1rem;
			}
		}
	}
}

// Стили для разделов сайта
.sections-menu {
	h3 {
		font-size: 2.4rem;
		font-weight: var(--font-weight-bold);
		color: var(--black-text);
		margin-bottom: 2rem;
	}

	&__list {
		display: flex;
		flex-direction: column;
		gap: 1rem;

		li a {
			display: block;
			padding: 1.2rem 0;
			color: var(--black-text);
			text-decoration: none;
			font-size: 1.8rem;
			font-weight: var(--font-weight-medium);
			border-bottom: 1px solid var(--border-color, #e0e0e0);
			transition: all 0.3s ease;

			&:hover {
				color: var(--accent-color);
				padding-left: 1rem;
			}
		}
	}
}

@media (max-width:1400px) {
	.hide-on-1400 {
		display:none;
	}
}
