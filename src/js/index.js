// Галерея и лайтбоксы от Fancybox
import { Fancybox } from '@fancyapps/ui';
import '@fancyapps/ui/dist/fancybox/fancybox.css';

Fancybox.bind('[data-fancybox]', {
	// Your custom options
});

// Мобильная навигация удалена

// Кнопка "Каталог услуг"
const catalogBtn = document.getElementById('catalogBtn');
const catalogOverlay = document.getElementById('catalogOverlay');

// Кнопка "Разделы сайта"
const sectionsBtn = document.getElementById('sectionsBtn');
const sectionsOverlay = document.getElementById('sectionsOverlay');

console.log('Menu elements:', { catalogBtn, catalogOverlay, sectionsBtn, sectionsOverlay });

// Функция для закрытия всех меню
function closeAllMenus() {
    catalogOverlay?.classList.remove('active');
    sectionsOverlay?.classList.remove('active');
    catalogBtn?.classList.remove('menu-btn--active');
    sectionsBtn?.classList.remove('menu-btn--active');
}

// Обработчик для кнопки "Каталог услуг"
if (catalogBtn && catalogOverlay) {
    catalogBtn.addEventListener('click', function() {
        console.log('Catalog button clicked!');

        // Если меню уже открыто - закрываем
        if (catalogOverlay.classList.contains('active')) {
            closeAllMenus();
        } else {
            // Закрываем другие меню и открываем каталог
            closeAllMenus();
            catalogOverlay.classList.add('active');
            catalogBtn.classList.add('menu-btn--active');
        }
    });
}

// Обработчик для кнопки "Разделы сайта"
if (sectionsBtn && sectionsOverlay) {
    sectionsBtn.addEventListener('click', function() {
        console.log('Sections button clicked!');

        // Если меню уже открыто - закрываем
        if (sectionsOverlay.classList.contains('active')) {
            closeAllMenus();
        } else {
            // Закрываем другие меню и открываем разделы
            closeAllMenus();
            sectionsOverlay.classList.add('active');
            sectionsBtn.classList.add('menu-btn--active');
        }
    });
}

// Закрытие меню при клике на overlay
catalogOverlay?.addEventListener('click', function(e) {
    if (e.target === this) {
        closeAllMenus();
    }
});

sectionsOverlay?.addEventListener('click', function(e) {
    if (e.target === this) {
        closeAllMenus();
    }
});

// Закрытие меню при нажатии Escape
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeAllMenus();
    }
});