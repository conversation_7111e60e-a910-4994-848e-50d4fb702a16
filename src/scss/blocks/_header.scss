.header {
	background-color: #c0e4f4;
	padding: 50px 0;

	&__row {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}
}

.header__nav {
	@include tablet {
		display: none;
	}
}
.sub-header {
	display: flex;
	flex-direction: row;
	justify-content: space-between;
	text-transform: uppercase;
	font-weight: 500;
}
.sub-header__socials {
}
.sub-header__icon {

}
.whatsapp {
}
.telegram {
}
.email {
}
.sub-header__calculate-phone {
}
.calculate {
}
.phone {
}