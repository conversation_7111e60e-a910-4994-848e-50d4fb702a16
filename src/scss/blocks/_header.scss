.header {
}

.header__nav {
	@include tablet {
		display: none;
	}
}
.sub-header {
	display: flex;
	flex-direction: row;
	justify-content: space-between;
	text-transform: uppercase;
	font-weight: var(--font-weight-semibold);
	font-size:1.3rem;
	color: var(--text-color);
	padding: 1rem 0;
	border-bottom: .1rem solid var(--borders-color);
	
	& a {
	color: var(--text-color);
	}
	& a:hover {
		color: var(--accent-color);
	}
	
}
.sub-header__socials {
}
.sub-header__icon {
	position: relative;
	padding-left: 3rem;
	display: inline-flex;
	align-items: center;

	&::before {
		content: '';
		position: absolute;
		left: 0;
		top: 50%;
		transform: translateY(-50%);
		width: 2.4rem;
		height: 2.4rem;
		background-size: contain;
		background-repeat: no-repeat;
		background-position: center;
	}

	// Иконки для разных типов
	&.whatsapp::before {
		background-image: url('./img/svgicons/whatsapp.svg');
	}

	&.telegram::before {
		background-image: url('./img/svgicons/telegram.svg');
	}

	&.email::before {
		background-image: url('./img/svgicons/email.svg');
	}

	&.calculate::before {
		background-image: url('./img/svgicons/calc.svg');
	}

	&.phone::before {
		background-image: url('./img/svgicons/call.svg');
	}
}
.phone {
	font-weight: var(--font-weight-bold);
}