// Галерея и лайтбоксы от Fancybox
import { Fancybox } from '@fancyapps/ui';
import '@fancyapps/ui/dist/fancybox/fancybox.css';

Fancybox.bind('[data-fancybox]', {
	// Your custom options
});

// Мобильная навигация удалена

const menuBtn = document.getElementById('menuBtn');

console.log('menuBtn element:', menuBtn); // Отладка

if (menuBtn) {
    menuBtn.addEventListener('click', function() {
        console.log('Button clicked!'); // Отладка
        this.classList.toggle('menu-btn--active');
        console.log('Classes after toggle:', this.className); // Отладка
    });
} else {
    console.error('Element with ID "menuBtn" not found!');
}