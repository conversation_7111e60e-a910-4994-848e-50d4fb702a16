@charset "UTF-8";
/* Base */ /* Reset and base styles  */
* {
  padding: 0px;
  margin: 0px;
  border: none;
}

*,
*::before,
*::after {
  box-sizing: border-box;
}

/* Links */
a, a:link, a:visited {
  text-decoration: none;
}

a:hover {
  text-decoration: none;
}

/* Common */
aside, nav, footer, header, section, main {
  display: block;
}

h1, h2, h3, h4, h5, h6, p {
  font-size: inherit;
  font-weight: inherit;
}

ul, ul li {
  list-style: none;
}

img {
  vertical-align: top;
}

img, svg {
  max-width: 100%;
  height: auto;
}

address {
  font-style: normal;
}

/* Form */
input, textarea, button, select {
  font-family: inherit;
  font-size: inherit;
  color: inherit;
  background-color: transparent;
}

input::-ms-clear {
  display: none;
}

button, input[type=submit] {
  display: inline-block;
  box-shadow: none;
  background-color: transparent;
  background: none;
  cursor: pointer;
}

input:focus, input:active,
button:focus, button:active {
  outline: none;
}

button::-moz-focus-inner {
  padding: 0;
  border: 0;
}

label {
  cursor: pointer;
}

legend {
  display: block;
}

:root {
  --container-width: 1200px;
  --container-padding: 15px;
  --font-main: "Onest", sans-serif;
  --font-accent: "Manrope", sans-serif;
  --font-titles: var(--font-main);
  --font-legacy: "mainfont";
  --font-weight-thin: 100;
  --font-weight-extralight: 200;
  --font-weight-light: 300;
  --font-weight-regular: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;
  --font-weight-black: 900;
  --font-thin: var(--font-main);
  --font-extralight: var(--font-main);
  --font-light: var(--font-main);
  --font-regular: var(--font-main);
  --font-medium: var(--font-main);
  --font-semibold: var(--font-main);
  --font-bold: var(--font-main);
  --font-extrabold: var(--font-main);
  --font-black: var(--font-main);
  --page-bg: #fff;
  --text-color: #000;
  --accent: #ac182c;
  --link-color: #2578c8;
  --laptop-size: 1199px;
  --tablet-size: 959px;
  --mobile-size: 599px;
  --accent-color: #FF752B;
  --borders-color: rgba(178,188,195,.5);
}

.dark {
  --page-bg: #252526;
  --text-color: #fff;
}

@font-face {
  font-family: FirasansBook;
  font-display: swap;
  src: url("../fonts/FirasansBook.woff2") format("woff2"), url("../fonts/FirasansBook.woff") format("woff");
  font-weight: 400;
  font-style: normal;
}
@font-face {
  font-family: Montserrat;
  font-display: swap;
  src: url("../fonts/Montserrat-Bold.woff2") format("woff2"), url("../fonts/Montserrat-Bold.woff") format("woff");
  font-weight: 700;
  font-style: normal;
}
@font-face {
  font-family: Montserrat;
  font-display: swap;
  src: url("../fonts/Montserrat-BoldItalic.woff2") format("woff2"), url("../fonts/Montserrat-BoldItalic.woff") format("woff");
  font-weight: 400;
  font-style: normal;
}
@font-face {
  font-family: Montserrat;
  font-display: swap;
  src: url("../fonts/Montserrat-Regular.woff2") format("woff2"), url("../fonts/Montserrat-Regular.woff") format("woff");
  font-weight: 400;
  font-style: normal;
}
html {
  font-size: 62.5%;
  height: 100%;
  width: 100%;
  scroll-behavior: smooth;
}
@media (max-width: 1500px) {
  html {
    font-size: 62.5%;
  }
}
@media (max-width: 992px) {
  html {
    font-size: 60%;
  }
}
@media (max-width: 767px) {
  html {
    font-size: 58%;
  }
}
@media (max-width: 576px) {
  html {
    font-size: 56%;
  }
}

body {
  background-color: var(--bgr-color__dark-gray-original);
  color: var(--text__on-dark);
  font-family: var(--font-main);
  font-size: 1.6rem;
}
@media (max-width: 767px) {
  body {
    font-size: 1.4rem;
  }
}
@media (max-width: 576px) {
  body {
    font-size: 1.2rem;
  }
}

.btn {
  display: inline-flex;
}

.btn-normal .btn-click {
  -webkit-box-shadow: inset 0px 0px 0px 1px var(--borders__on-dark);
  box-shadow: inset 0px 0px 0px 1px var(--borders__on-dark);
}

.btn-normal .btn-click-white {
  -webkit-box-shadow: inset 0px 0px 0px 1px var(--headers__on-dark);
  box-shadow: inset 0px 0px 0px 1px var(--headers__on-dark);
}

.btn-click {
  cursor: pointer;
  border: 0;
  color: var(--text__on-dark);
  background: transparent;
  border-radius: 10rem !important;
  min-width: 1rem;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  text-decoration: none;
  will-change: transform;
  outline: 0;
  transform: translateZ(0) rotate(0.001deg);
  padding: 1.7rem 3rem !important;
  margin: 0 !important;
  text-decoration: none !important;
  font-size: 1.4rem;
}
@media (max-width: 992px) {
  .btn-click {
    font-size: 1.2rem;
    height: 4.8rem;
    padding: 1.7rem 2rem !important;
  }
}

.btn-click-small-padding {
  padding: 1rem 1.5rem !important;
}

.btn-click-white {
  cursor: pointer;
  border: 0;
  color: var(--headers__on-dark);
  background: transparent;
  border-radius: 10rem !important;
  min-width: 1rem;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  text-decoration: none;
  will-change: transform;
  outline: 0;
  transform: translateZ(0) rotate(0.001deg);
  padding: 1.7rem 3rem !important;
  margin: 0 !important;
  text-decoration: none !important;
  font-size: 1.4rem;
}
@media (max-width: 992px) {
  .btn-click-white {
    font-size: 1.2rem;
  }
}

.btn-bgr-dark {
  background: var(--bgr-color__dark-gray-original);
}

.btn-bgr-white {
  background: var(--headers__on-dark);
}

.btn-fill {
  background: var(--borders__on-dark);
  position: absolute;
  width: 150%;
  height: 200%;
  border-radius: 50%;
  top: -50%;
  left: -25%;
  transform: translate3d(0, -76%, 0);
  will-change: transform;
  transition: background-color ease-in-out 0.25s;
}

.btn-text {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  z-index: 2;
  color: var(--text__on-dark);
  position: relative;
  transform: rotate(0.001deg);
  pointer-events: none;
  will-change: transform, color;
}

.btn-normal .btn-text .btn-text-inner {
  color: var(--headers__on-dark) !important;
  display: flex;
  align-items: center;
  gap: 1rem;
  justify-content: center;
}

.pointernone {
  pointer-events: none;
}

.zindex {
  z-index: 9999999999999;
  pointer-events: all;
}

.btn-text-inner img {
  width: 2.4rem;
}

@media (max-width: 992) {
  .btn-text-inner img {
    width: 1.4rem;
  }
}
@media (min-width: 993) {
  .btn-text-inner img {
    width: 1.5rem;
  }
}
@media (max-width: 1400) {
  .btn-text-inner img {
    width: 1.6rem;
  }
}
.uppercase {
  text-transform: uppercase;
}

.main-button {
  font-size: 1.4rem;
  color: var(--headers__on-dark);
  background: var(--bgr-color__dark-gray-original);
  padding: 0.3rem 0.3rem 0.3rem 2.5rem;
  border: 1px solid var(--headers__on-dark);
  border-radius: 50rem;
  align-items: center;
  height: 5.8rem;
  display: flex;
  justify-content: flex-start;
  position: relative;
  z-index: 1;
}
@media (max-width: 992px) {
  .main-button {
    font-size: 1.2rem;
    padding: 0.3rem 0.3rem 0.3rem 0.9rem;
    height: 4.6rem;
  }
}
.main-button__icon {
  border-radius: 10rem;
  background: linear-gradient(180deg, #FFF 0%, #BABABA 100%);
  box-shadow: -1px 0px 10.3px 0px rgba(0, 0, 0, 0.11);
  width: 5.2rem;
  height: 5.2rem;
  display: flex;
  justify-content: end;
  align-items: center;
  margin-left: 1.5rem;
  position: absolute;
  right: 0.2rem;
  transition: var(--animation-smooth);
  z-index: -1;
}
.main-button__icon img {
  height: 50%;
  padding-right: 1.3rem;
}
@media (max-width: 992px) {
  .main-button__icon img {
    padding-right: 1rem;
  }
}
@media (max-width: 992px) {
  .main-button__icon {
    width: 4rem;
    height: 4rem;
    margin-left: 1rem;
  }
}
.main-button:hover {
  color: var(--borders__on-dark);
}
.main-button:hover .main-button__icon {
  width: calc(100% - 0.4rem);
}
.main-button__dummy {
  visibility: hidden;
  width: 5.2rem;
  height: 5.2rem;
  margin-left: 1.5rem;
}
@media (max-width: 992px) {
  .main-button__dummy {
    width: 4rem;
    height: 4rem;
    margin-left: 1rem;
  }
}

::-moz-selection {
  background-color: var(--borders__on-dark);
  color: var(--headers__on-dark);
}

::selection {
  background-color: var(--borders__on-dark);
  color: var(--headers__on-dark);
}

.docs {
  display: grid;
  line-height: 1.5;
}
.docs p {
  margin: 1rem 0;
}
.docs ul,
.docs ol {
  padding-left: 2rem;
}
.docs ul li,
.docs ol li {
  list-style: disc;
  margin-bottom: 0.5rem;
}
.docs ol li {
  list-style: decimal;
}
.docs section, .docs section.docs {
  padding: 40px 0;
}
.docs section + section {
  border-top: 1px solid #dae5e9;
}
.docs small {
  font-size: 1rem;
  color: rgb(172, 172, 172);
}
.docs .title-1:first-child,
.docs .title-2:first-child {
  margin-top: 0 !important;
}

.test {
  width: 600px;
  height: 300px;
  margin: 50px auto;
  background-color: #999;
  background-position: center center; /* x y */
  background-size: cover;
  background-repeat: no-repeat;
  background-image: url("./../img/project-02.jpg");
}
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .test {
    background-image: url("./../img/<EMAIL>");
  }
}

.test-2 {
  width: 600px;
  height: 300px;
  margin: 50px auto;
  background-color: #999;
  background-position: center center; /* x y */
  background-size: cover;
  background-repeat: no-repeat;
  background-image: image-set(url("./../img/project-02.jpg") 1x, url("./../img/<EMAIL>") 2x);
}

.font-1 {
  font-family: "Montserrat";
  font-weight: 700;
  font-style: italic;
}

.font-2 {
  font-family: "FirasansBook";
  font-weight: 400;
}

/* Отключить при необходимости */
.none {
  display: none !important;
}

.visually-hidden {
  position: absolute;
  width: 1px;
  height: 1px;
  margin: -1px;
  border: 0;
  padding: 0;
  white-space: nowrap;
  clip-path: inset(100%);
  clip: rect(0 0 0 0);
  overflow: hidden;
}

.no-scroll {
  overflow-y: hidden;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.text-center {
  text-align: center;
}

.d-flex {
  display: flex;
}

.flex-center {
  justify-content: center;
}

.container {
  margin: 0 auto;
  border-left: 1px solid var(--borders__on-dark);
  border-right: 1px solid var(--borders__on-dark);
}
@media (min-width: 1700px) {
  .container {
    margin: auto 8rem;
    padding: 0 5rem;
  }
}
@media (min-width: 993px) and (max-width: 1699px) {
  .container {
    margin: auto 5rem;
    padding: 0 3rem;
  }
}
@media (max-width: 992px) {
  .container {
    margin: auto 3rem;
    padding: 0 2rem;
  }
}
@media (max-width: 767px) {
  .container {
    margin: auto 2rem;
    padding: 0 2rem;
  }
}
@media (max-width: 576px) {
  .container {
    margin: auto 1.6rem;
    padding: 0 1.6rem;
  }
}

.container-grid-section {
  margin: 0 auto;
  height: 100%;
  min-height: inherit;
  border-left: 1px solid var(--borders__on-dark);
  border-right: 1px solid var(--borders__on-dark);
  position: relative;
  z-index: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
@media (min-width: 1700px) {
  .container-grid-section {
    margin: auto 8rem;
  }
}
@media (min-width: 993px) and (max-width: 1699px) {
  .container-grid-section {
    margin: auto 5rem;
  }
}
@media (max-width: 992px) {
  .container-grid-section {
    margin: auto 3rem;
  }
}
@media (max-width: 767px) {
  .container-grid-section {
    margin: auto 2rem;
  }
}
@media (max-width: 576px) {
  .container-grid-section {
    margin: auto 1.6rem;
  }
}

.container-no-border {
  margin: 0 auto;
}
@media (min-width: 1700px) {
  .container-no-border {
    margin: auto 8rem;
    padding: 0 5rem;
  }
}
@media (min-width: 993px) and (max-width: 1699px) {
  .container-no-border {
    margin: auto 5rem;
    padding: 0 3rem;
  }
}
@media (max-width: 992px) {
  .container-no-border {
    margin: auto 3rem;
    padding: 0 2rem;
  }
}
@media (max-width: 767px) {
  .container-no-border {
    margin: auto 2rem;
    padding: 0 2rem;
  }
}
@media (max-width: 576px) {
  .container-no-border {
    margin: auto 1.6rem;
    padding: 0 1.6rem;
  }
}

.container-inside-container {
  position: relative;
}
@media (min-width: 1700px) {
  .container-inside-container {
    padding: 0 5rem;
  }
}
@media (min-width: 993px) and (max-width: 1699px) {
  .container-inside-container {
    padding: 0 3rem;
  }
}
@media (max-width: 992px) {
  .container-inside-container {
    padding: 0 2rem;
  }
}
@media (max-width: 767px) {
  .container-inside-container {
    padding: 0 2rem;
  }
}
@media (max-width: 576px) {
  .container-inside-container {
    padding: 0 1.6rem;
  }
}

.lines {
  pointer-events: none;
  position: absolute;
  z-index: -5;
  background: transparent;
  width: 100%;
  height: 100%;
  opacity: 1;
  display: flex;
  flex-grow: 0;
  flex-shrink: 0;
  flex-wrap: nowrap;
}
.lines__empty {
  width: calc(25% - 1px);
  background: transparent;
  height: 100%;
  opacity: 0.1;
}
@media (max-width: 992px) {
  .lines__empty {
    width: calc(33.33333% - 1px);
  }
}
.lines__line {
  width: 1px;
  background-color: var(--borders__on-dark);
  height: 100%;
}
.lines__line--invisible {
  width: 1px;
  background-color: transparent;
  height: 100%;
}

.lines-md-2 {
  pointer-events: none;
  position: absolute;
  z-index: -5;
  background: transparent;
  width: 100%;
  height: 100%;
  opacity: 1;
  display: flex;
  flex-grow: 0;
  flex-shrink: 0;
  flex-wrap: nowrap;
}
.lines-md-2__empty {
  width: calc(25% - 1px);
  background: transparent;
  height: 100%;
  opacity: 0.1;
}
@media (max-width: 992px) {
  .lines-md-2__empty {
    width: calc(50% - 1px);
  }
}
.lines-md-2__line {
  width: 1px;
  background-color: var(--borders__on-dark);
  height: 100%;
}

.main-menu-open .container {
  border-left: 1px solid rgba(255, 0, 0, 0);
  border-right: 1px solid rgba(255, 0, 0, 0);
}

.disable-padding {
  padding: 0 !important;
}

.borders-l-r {
  border-left: 1px solid var(--borders__on-dark);
  border-right: 1px solid var(--borders__on-dark);
}

.borders-t {
  border-top: 1px solid var(--borders__on-dark);
}

.borders-b {
  border-bottom: 1px solid var(--borders__on-dark);
}

.borders-t-b {
  border-top: 1px solid var(--borders__on-dark);
  border-bottom: 1px solid var(--borders__on-dark);
}

.borders-all {
  border: 1px solid var(--borders__on-dark);
}

.disable-borders {
  border: none !important;
}

.flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.flex_fw {
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.flex_aic {
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.flex_aife {
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: flex-end;
}

.flex_jcc {
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.flex_jcsb {
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
}

@media (max-width: 767px) {
  .flex_mobile-column {
    flex-direction: column;
  }
}
.container-right {
  /* overflow-x: hidden; */
  padding-left: calc((100% - var(--container-width)) / 2 + var(--container-padding));
}
@media (max-width: var(--laptop-size)) {
  .container-right {
    padding-left: var(--container-padding);
  }
}

.grid-section__content {
  font-size: 2.5rem;
  line-height: 140%;
}

.container-left {
  /* overflow-x: hidden; */
  padding-right: calc((100% - var(--container-width)) / 2 + var(--container-padding));
}
@media (max-width: var(--laptop-size)) {
  .container-left {
    padding-right: var(--container-padding);
  }
}

.mt-auto {
  margin-top: auto;
}

.mt-50 {
  margin-top: 5rem;
}

.pt-50 {
  padding-top: 5rem;
}
@media (max-width: 992px) {
  .pt-50 {
    padding-bottom: 4rem;
  }
}
@media (max-width: 767px) {
  .pt-50 {
    padding-bottom: 2rem;
  }
}
@media (max-width: 567px) {
  .pt-50 {
    padding-bottom: 1.6rem;
  }
}

.pb-50 {
  padding-bottom: 5rem;
}
@media (max-width: 992px) {
  .pb-50 {
    padding-bottom: 4rem;
  }
}
@media (max-width: 767px) {
  .pb-50 {
    padding-bottom: 2rem;
  }
}
@media (max-width: 567px) {
  .pb-50 {
    padding-bottom: 1.6rem;
  }
}

.pb-30 {
  padding-bottom: 3rem;
}
@media (max-width: 992px) {
  .pb-30 {
    padding-bottom: 3rem;
  }
}
@media (max-width: 767px) {
  .pb-30 {
    padding-bottom: 2rem;
  }
}
@media (max-width: 567px) {
  .pb-30 {
    padding-bottom: 1.6rem;
  }
}

.gap-3 {
  gap: 3rem;
}

.mb-3 {
  margin-bottom: 3rem;
}
@media (max-width: 992px) {
  .mb-3 {
    margin-bottom: 2rem;
  }
}
@media (max-width: 567px) {
  .mb-3 {
    margin-bottom: 1.6rem;
  }
}

.spares-block ul {
  margin-bottom: 1rem;
}

.spares-block ul li {
  list-style: disc;
  list-style-position: inside;
  padding-bottom: 1rem;
  line-height: 150%;
}

.mw770 {
  max-width: 77rem;
}
@media (max-width: 992px) {
  .mw770 {
    max-width: 100%;
  }
}
.m-0-auto {
  margin: 0 auto;
}

html, body {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.footer {
  margin-top: auto;
}

.footer {
  padding: 60px 0;
  background-color: #e3e3e3;
}

/* Blocks */
.footer {
  background-color: rgb(39, 39, 39);
  padding: 50px 0;
  font-size: 32px;
  color: #fff;
}
.footer h1 {
  font-size: 32px;
}
.footer a {
  color: #fff;
  text-decoration: underline;
}
@media (max-width: 1200px) {
  .footer {
    font-size: 26px;
  }
}

.footer__copyright {
  padding: 10px 0;
  font-size: 16px;
}

@media (max-width: 1220px) {
  .header__nav {
    display: none;
  }
}

.sub-header {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  text-transform: uppercase;
  font-weight: var(--font-weight-semibold);
  font-size: 1.3rem;
  color: var(--text-color);
  padding: 1rem 0;
  border-bottom: 0.1rem solid var(--borders-color);
}
.sub-header a {
  color: var(--text-color);
}
.sub-header a:hover {
  color: var(--accent-color);
}

.sub-header__icon {
  position: relative;
  padding-left: 3rem;
  display: inline-flex;
  align-items: center;
}
.sub-header__icon::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 2.4rem;
  height: 2.4rem;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}
.sub-header__icon.whatsapp::before {
  background-image: url("./img/svgicons/whatsapp.svg");
}
.sub-header__icon.telegram::before {
  background-image: url("./img/svgicons/telegram.svg");
}
.sub-header__icon.email::before {
  background-image: url("./img/svgicons/email.svg");
}
.sub-header__icon.calculate::before {
  background-image: url("./img/svgicons/calc.svg");
}
.sub-header__icon.phone::before {
  background-image: url("./img/svgicons/call.svg");
}

.phone {
  font-weight: var(--font-weight-bold);
}

.icons-wrapper {
  padding: 30px 0;
  display: flex;
  column-gap: 30px;
}

.icon {
  fill: transparent;
  stroke: transparent;
  width: 62px;
  height: 62px;
}

.icon--heart-line {
  fill: rgb(241, 68, 131);
}

.icon--id-card-line {
  fill: rgb(51, 51, 51);
}

.icon--search-line {
  fill: rgb(28, 176, 80);
}

.icon--user-star {
  fill: rgb(26, 134, 235);
}

.icon--user {
  stroke: rgb(26, 134, 235);
  transition: all 0.2s ease-in;
}
.icon--user:hover {
  stroke: rgb(17, 193, 90);
}

.logo {
  font-size: 32px;
}

.mobile-nav {
  position: fixed;
  top: -100%;
  width: 100%;
  height: 100%;
  z-index: 99;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: 40px;
  padding-bottom: 40px;
  background: #8ccae6;
  transition: all 0.2s ease-in;
}

.mobile-nav--open {
  top: 0;
}

.mobile-nav a {
  color: #fff;
}

.mobile-nav__list {
  display: flex;
  flex-direction: column;
  align-items: center;
  row-gap: 20px;
  font-size: 28px;
}
.mobile-nav__list .active {
  opacity: 0.5;
}

/* Nav Icon */
.mobile-nav-btn {
  --time: 0.1s;
  --width: 40px;
  --height: 30px;
  --line-height: 4px;
  --spacing: 6px;
  --color: #000;
  --radius: 4px;
  /* Fixed height and width */
  /* height: var(--height); */
  /* width: var(--width); */
  /* Dynamic height and width */
  height: calc(var(--line-height) * 3 + var(--spacing) * 2);
  width: var(--width);
  display: flex;
  justify-content: center;
  align-items: center;
}

.nav-icon {
  position: relative;
  width: var(--width);
  height: var(--line-height);
  background-color: var(--color);
  border-radius: var(--radius);
}

.nav-icon::before,
.nav-icon::after {
  content: "";
  display: block;
  position: absolute;
  left: 0;
  width: var(--width);
  height: var(--line-height);
  border-radius: var(--radius);
  background-color: var(--color);
  transition: transform var(--time) ease-in, top var(--time) linear var(--time);
}

.nav-icon::before {
  /* top: calc(var(--line-height) * -2); */
  top: calc(-1 * (var(--line-height) + var(--spacing)));
}

.nav-icon::after {
  /* top: calc(var(--line-height) * 2); */
  top: calc(var(--line-height) + var(--spacing));
}

.nav-icon.nav-icon--active {
  background-color: transparent;
}

.nav-icon.nav-icon--active::before,
.nav-icon.nav-icon--active::after {
  top: 0;
  transition: top var(--time) linear, transform var(--time) ease-in var(--time);
}

.nav-icon.nav-icon--active::before {
  transform: rotate(45deg);
}

.nav-icon.nav-icon--active::after {
  transform: rotate(-45deg);
}

/* Layout */
.mobile-nav-btn {
  z-index: 999;
}

.nav {
  font-size: 18px;
}

.nav__list {
  display: flex;
  column-gap: 30px;
}

.title-1 {
  margin: 1em 0 0.5em;
  font-size: 38px;
  font-weight: 700;
  font-family: var(--font-titles);
}

.title-2 {
  margin: 1em 0 0.5em;
  font-size: 32px;
  font-weight: 700;
  font-family: var(--font-titles);
}

.title-3 {
  margin: 1em 0 0.5em;
  font-size: 26px;
  font-weight: 700;
  font-family: var(--font-titles);
}

.title-4 {
  margin: 1em 0 0.5em;
  font-size: 18px;
  font-weight: 700;
  font-family: var(--font-titles);
}

/* No styles code below. Only in modules */
/* Не пишите CSS код ниже. Только в подключаемых файлах */
/*# sourceMappingURL=data:application/json;charset=utf8;base64,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 */
