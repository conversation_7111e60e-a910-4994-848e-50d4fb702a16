html {
    font-size: calc(100vw / 144);
    height: 100%;
    width: 100%;
	scroll-behavior: smooth;

	@media (max-width: 1500px) {
		font-size: .6944444444vw
	}
	@media (max-width: 992px) {
		font-size: 1.3037809648vw
	}
	@media (max-width: 767px) {
		font-size: 1.7361111111vw
	}
	@media (max-width: 576px) {
		font-size: 2.7777777778vw
	}
}

body {
	background-color: var(--bgr-color__dark-gray-original);
	color: var(--text__on-dark);
    font-family: var(--font-main);
    font-size: 1.6rem;
    @media (max-width: 767px) {
        font-size: 1.4rem;
    }
    @media (max-width: 576px) {
        font-size: 1.2rem;
    }
}

.btn {
    display: inline-flex;
}
.btn-normal .btn-click {
    -webkit-box-shadow: inset 0px 0px 0px 1px var(--borders__on-dark);
    box-shadow: inset 0px 0px 0px 1px var(--borders__on-dark);
}
.btn-normal .btn-click-white {
    -webkit-box-shadow: inset 0px 0px 0px 1px var(--headers__on-dark);
    box-shadow: inset 0px 0px 0px 1px var(--headers__on-dark);
}
.btn-click {
    cursor: pointer;
    border: 0;
    color: var(--text__on-dark);
    background: transparent;
    border-radius: 10rem !important;
    min-width: 1rem;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    text-decoration: none;
    will-change: transform;
    outline: 0;
    transform: translateZ(0) rotate(0.001deg);
	padding: 1.7rem 3rem !important;
	margin: 0 !important;
	text-decoration: none !important;
    font-size: 1.4rem;

    @media (max-width: 992px) {
		font-size: 1.2rem;
        height: 4.8rem;
        padding: 1.7rem 2rem !important;
	}
}
.btn-click-small-padding {
	padding: 1rem 1.5rem !important;
}

.btn-click-white {
    cursor: pointer;
    border: 0;
    color: var(--headers__on-dark);
    background: transparent;
    border-radius: 10rem !important;
    min-width: 1rem;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    text-decoration: none;
    will-change: transform;
    outline: 0;
    transform: translateZ(0) rotate(0.001deg);
	padding: 1.7rem 3rem !important;
	margin: 0 !important;
	text-decoration: none !important;
    font-size: 1.4rem;

    @media (max-width: 992px) {
		font-size: 1.2rem
	}
}


.btn-bgr-dark {
    background: var(--bgr-color__dark-gray-original);
}
.btn-bgr-white {
    background: var(--headers__on-dark);
}
.btn-fill {
    background: var(--borders__on-dark);
    position: absolute;
    width: 150%;
    height: 200%;
    border-radius: 50%;
    top: -50%;
    left: -25%;
    transform: translate3d(0,-76%,0);
    will-change: transform;
    transition: background-color ease-in-out .25s;
}
.btn-text {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    z-index: 2;
    color: var(--text__on-dark);
    position: relative;
    transform: rotate(0.001deg);
    pointer-events: none;
    will-change: transform, color;
}
.btn-normal .btn-text .btn-text-inner {
    color: var(--headers__on-dark) !important;
	display: flex;
    align-items: center;
    gap: 1rem;
    justify-content: center;
}
.pointernone {
    pointer-events: none;
}
.zindex {
    z-index: 9999999999999;
    pointer-events: all;
}

.btn-text-inner {
	img {
		width: 2.4rem;	
	}
}
@media (max-width: 992) {
	.btn-text-inner { img { width: 1.4rem; } }
}
@media (min-width: 993) {
	.btn-text-inner { img { width: 1.5rem; } }
} 
@media (max-width: 1400) {
	.btn-text-inner { img { width: 1.6rem; } }
}
.uppercase {
	text-transform: uppercase;
}


.main-button {
    font-size: 1.4rem;
    color: var(--headers__on-dark);
    background: var(--bgr-color__dark-gray-original);
    padding: 0.3rem 0.3rem 0.3rem 2.5rem;
    border: 1px solid var(--headers__on-dark);
    border-radius: 50rem;
    align-items: center;
    height: 5.8rem;
    display: flex;
    justify-content: flex-start;
    position: relative;
    z-index: 1;

    @media (max-width: 992px) {
		font-size: 1.2rem;
        padding: 0.3rem 0.3rem 0.3rem 0.9rem;
        height: 4.6rem;
	}

    
    &__icon {
        border-radius: 10rem;
        background: linear-gradient(180deg, #FFF 0%, #BABABA 100%);
        box-shadow: -1px 0px 10.3px 0px rgba(0, 0, 0, 0.11);
        width: 5.2rem;
        height: 5.2rem;
        display: flex;
        justify-content: end;
        align-items: center;
        margin-left: 1.5rem;
        position: absolute;
        right: 0.2rem;
        transition: var(--animation-smooth);
        z-index: -1;

        & img {
            height: 50%;
            padding-right: calc(2.6rem / 2);

            @media (max-width: 992px) {
                padding-right: calc(2rem / 2);
            }
        }

        @media (max-width: 992px) {
            width: 4rem;
            height: 4rem;
            margin-left: 1rem;
        }
    }
    &:hover {
        color: var(--borders__on-dark);
    }
    &:hover &__icon {
        width: calc(100% - .4rem);
    }

    &__dummy {
        visibility: hidden;
        width: 5.2rem;
        height: 5.2rem;
        margin-left: 1.5rem;

        @media (max-width: 992px) {
            width: 4rem;
            height: 4rem;
            margin-left: 1rem;
        }
    }
}

::-moz-selection {
    background-color: var(--borders__on-dark);
    color: var(--headers__on-dark);
}

::selection {
    background-color: var(--borders__on-dark);
    color: var(--headers__on-dark);
}