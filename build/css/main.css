@charset "UTF-8";
/* Base */ /* Reset and base styles  */
* {
  padding: 0px;
  margin: 0px;
  border: none;
}

*,
*::before,
*::after {
  box-sizing: border-box;
}

/* Links */
a, a:link, a:visited {
  text-decoration: none;
}

a:hover {
  text-decoration: none;
}

/* Common */
aside, nav, footer, header, section, main {
  display: block;
}

h1, h2, h3, h4, h5, h6, p {
  font-size: inherit;
  font-weight: inherit;
}

ul, ul li {
  list-style: none;
}

img {
  vertical-align: top;
}

img, svg {
  max-width: 100%;
  height: auto;
}

address {
  font-style: normal;
}

/* Form */
input, textarea, button, select {
  font-family: inherit;
  font-size: inherit;
  color: inherit;
  background-color: transparent;
}

input::-ms-clear {
  display: none;
}

button, input[type=submit] {
  display: inline-block;
  box-shadow: none;
  background-color: transparent;
  background: none;
  cursor: pointer;
}

input:focus, input:active,
button:focus, button:active {
  outline: none;
}

button::-moz-focus-inner {
  padding: 0;
  border: 0;
}

label {
  cursor: pointer;
}

legend {
  display: block;
}

:root {
  --container-width: 1200px;
  --container-padding: 15px;
  --font-main: "Onest", sans-serif;
  --font-accent: "Manrope", sans-serif;
  --font-titles: var(--font-main);
  --font-legacy: "mainfont";
  --font-weight-thin: 100;
  --font-weight-extralight: 200;
  --font-weight-light: 300;
  --font-weight-regular: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;
  --font-weight-black: 900;
  --font-thin: var(--font-main);
  --font-extralight: var(--font-main);
  --font-light: var(--font-main);
  --font-regular: var(--font-main);
  --font-medium: var(--font-main);
  --font-semibold: var(--font-main);
  --font-bold: var(--font-main);
  --font-extrabold: var(--font-main);
  --font-black: var(--font-main);
  --page-bg: #fff;
  --text-color: #000;
  --accent: #ac182c;
  --link-color: #2578c8;
  --laptop-size: 1199px;
  --tablet-size: 959px;
  --mobile-size: 599px;
  --accent-color: #FF752B;
  --alternate-accent-color: #15A0E5;
  --grey-text: #707070;
  --white-text: #FFF;
  --black-text: #282828;
  --borders-color: rgba(178,188,195,.5);
}

.dark {
  --page-bg: #252526;
  --text-color: #fff;
}

@font-face {
  font-family: FirasansBook;
  font-display: swap;
  src: url("../fonts/FirasansBook.woff2") format("woff2"), url("../fonts/FirasansBook.woff") format("woff");
  font-weight: 400;
  font-style: normal;
}
@font-face {
  font-family: Montserrat;
  font-display: swap;
  src: url("../fonts/Montserrat-Bold.woff2") format("woff2"), url("../fonts/Montserrat-Bold.woff") format("woff");
  font-weight: 700;
  font-style: normal;
}
@font-face {
  font-family: Montserrat;
  font-display: swap;
  src: url("../fonts/Montserrat-BoldItalic.woff2") format("woff2"), url("../fonts/Montserrat-BoldItalic.woff") format("woff");
  font-weight: 400;
  font-style: normal;
}
@font-face {
  font-family: Montserrat;
  font-display: swap;
  src: url("../fonts/Montserrat-Regular.woff2") format("woff2"), url("../fonts/Montserrat-Regular.woff") format("woff");
  font-weight: 400;
  font-style: normal;
}
html {
  font-size: 62.5%;
  height: 100%;
  width: 100%;
  scroll-behavior: smooth;
}
@media (max-width: 1500px) {
  html {
    font-size: 62.5%;
  }
}
@media (max-width: 992px) {
  html {
    font-size: 60%;
  }
}
@media (max-width: 767px) {
  html {
    font-size: 58%;
  }
}
@media (max-width: 576px) {
  html {
    font-size: 56%;
  }
}

body {
  background-color: var(--bgr-color__dark-gray-original);
  color: var(--text__on-dark);
  font-family: var(--font-main);
  font-size: 1.6rem;
}
@media (max-width: 767px) {
  body {
    font-size: 1.4rem;
  }
}
@media (max-width: 576px) {
  body {
    font-size: 1.2rem;
  }
}

.btn {
  display: inline-flex;
}

.btn-normal .btn-click {
  -webkit-box-shadow: inset 0px 0px 0px 1px var(--borders__on-dark);
  box-shadow: inset 0px 0px 0px 1px var(--borders__on-dark);
}

.btn-normal .btn-click-white {
  -webkit-box-shadow: inset 0px 0px 0px 1px var(--headers__on-dark);
  box-shadow: inset 0px 0px 0px 1px var(--headers__on-dark);
}

.btn-click {
  cursor: pointer;
  border: 0;
  color: var(--text__on-dark);
  background: transparent;
  border-radius: 10rem !important;
  min-width: 1rem;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  text-decoration: none;
  will-change: transform;
  outline: 0;
  transform: translateZ(0) rotate(0.001deg);
  padding: 1.7rem 3rem !important;
  margin: 0 !important;
  text-decoration: none !important;
  font-size: 1.4rem;
}
@media (max-width: 992px) {
  .btn-click {
    font-size: 1.2rem;
    height: 4.8rem;
    padding: 1.7rem 2rem !important;
  }
}

.btn-click-small-padding {
  padding: 1rem 1.5rem !important;
}

.btn-click-white {
  cursor: pointer;
  border: 0;
  color: var(--headers__on-dark);
  background: transparent;
  border-radius: 10rem !important;
  min-width: 1rem;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  text-decoration: none;
  will-change: transform;
  outline: 0;
  transform: translateZ(0) rotate(0.001deg);
  padding: 1.7rem 3rem !important;
  margin: 0 !important;
  text-decoration: none !important;
  font-size: 1.4rem;
}
@media (max-width: 992px) {
  .btn-click-white {
    font-size: 1.2rem;
  }
}

.btn-bgr-dark {
  background: var(--bgr-color__dark-gray-original);
}

.btn-bgr-white {
  background: var(--headers__on-dark);
}

.btn-fill {
  background: var(--borders__on-dark);
  position: absolute;
  width: 150%;
  height: 200%;
  border-radius: 50%;
  top: -50%;
  left: -25%;
  transform: translate3d(0, -76%, 0);
  will-change: transform;
  transition: background-color ease-in-out 0.25s;
}

.btn-text {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  z-index: 2;
  color: var(--text__on-dark);
  position: relative;
  transform: rotate(0.001deg);
  pointer-events: none;
  will-change: transform, color;
}

.btn-normal .btn-text .btn-text-inner {
  color: var(--headers__on-dark) !important;
  display: flex;
  align-items: center;
  gap: 1rem;
  justify-content: center;
}

.pointernone {
  pointer-events: none;
}

.zindex {
  z-index: 9999999999999;
  pointer-events: all;
}

.btn-text-inner img {
  width: 2.4rem;
}

@media (max-width: 992) {
  .btn-text-inner img {
    width: 1.4rem;
  }
}
@media (min-width: 993) {
  .btn-text-inner img {
    width: 1.5rem;
  }
}
@media (max-width: 1400) {
  .btn-text-inner img {
    width: 1.6rem;
  }
}
.uppercase {
  text-transform: uppercase;
}

.main-button {
  font-size: 1.4rem;
  color: var(--headers__on-dark);
  background: var(--bgr-color__dark-gray-original);
  padding: 0.3rem 0.3rem 0.3rem 2.5rem;
  border: 1px solid var(--headers__on-dark);
  border-radius: 50rem;
  align-items: center;
  height: 5.8rem;
  display: flex;
  justify-content: flex-start;
  position: relative;
  z-index: 1;
}
@media (max-width: 992px) {
  .main-button {
    font-size: 1.2rem;
    padding: 0.3rem 0.3rem 0.3rem 0.9rem;
    height: 4.6rem;
  }
}
.main-button__icon {
  border-radius: 10rem;
  background: linear-gradient(180deg, #FFF 0%, #BABABA 100%);
  box-shadow: -1px 0px 10.3px 0px rgba(0, 0, 0, 0.11);
  width: 5.2rem;
  height: 5.2rem;
  display: flex;
  justify-content: end;
  align-items: center;
  margin-left: 1.5rem;
  position: absolute;
  right: 0.2rem;
  transition: var(--animation-smooth);
  z-index: -1;
}
.main-button__icon img {
  height: 50%;
  padding-right: 1.3rem;
}
@media (max-width: 992px) {
  .main-button__icon img {
    padding-right: 1rem;
  }
}
@media (max-width: 992px) {
  .main-button__icon {
    width: 4rem;
    height: 4rem;
    margin-left: 1rem;
  }
}
.main-button:hover {
  color: var(--borders__on-dark);
}
.main-button:hover .main-button__icon {
  width: calc(100% - 0.4rem);
}
.main-button__dummy {
  visibility: hidden;
  width: 5.2rem;
  height: 5.2rem;
  margin-left: 1.5rem;
}
@media (max-width: 992px) {
  .main-button__dummy {
    width: 4rem;
    height: 4rem;
    margin-left: 1rem;
  }
}

::-moz-selection {
  background-color: var(--borders__on-dark);
  color: var(--headers__on-dark);
}

::selection {
  background-color: var(--borders__on-dark);
  color: var(--headers__on-dark);
}

.docs {
  display: grid;
  line-height: 1.5;
}
.docs p {
  margin: 1rem 0;
}
.docs ul,
.docs ol {
  padding-left: 2rem;
}
.docs ul li,
.docs ol li {
  list-style: disc;
  margin-bottom: 0.5rem;
}
.docs ol li {
  list-style: decimal;
}
.docs section, .docs section.docs {
  padding: 40px 0;
}
.docs section + section {
  border-top: 1px solid #dae5e9;
}
.docs small {
  font-size: 1rem;
  color: rgb(172, 172, 172);
}
.docs .title-1:first-child,
.docs .title-2:first-child {
  margin-top: 0 !important;
}

.test {
  width: 600px;
  height: 300px;
  margin: 50px auto;
  background-color: #999;
  background-position: center center; /* x y */
  background-size: cover;
  background-repeat: no-repeat;
  background-image: url("./../img/project-02.jpg");
}
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .test {
    background-image: url("./../img/<EMAIL>");
  }
}

.test-2 {
  width: 600px;
  height: 300px;
  margin: 50px auto;
  background-color: #999;
  background-position: center center; /* x y */
  background-size: cover;
  background-repeat: no-repeat;
  background-image: image-set(url("./../img/project-02.jpg") 1x, url("./../img/<EMAIL>") 2x);
}

.font-1 {
  font-family: "Montserrat";
  font-weight: 700;
  font-style: italic;
}

.font-2 {
  font-family: "FirasansBook";
  font-weight: 400;
}

/* Отключить при необходимости */
.none {
  display: none !important;
}

.visually-hidden {
  position: absolute;
  width: 1px;
  height: 1px;
  margin: -1px;
  border: 0;
  padding: 0;
  white-space: nowrap;
  clip-path: inset(100%);
  clip: rect(0 0 0 0);
  overflow: hidden;
}

.no-scroll {
  overflow-y: hidden;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.text-center {
  text-align: center;
}

.d-flex {
  display: flex;
}

.flex-center {
  justify-content: center;
}

.container {
  margin: 0 auto;
  border-left: 1px solid var(--borders__on-dark);
  border-right: 1px solid var(--borders__on-dark);
}
@media (min-width: 1700px) {
  .container {
    margin: auto 8rem;
    padding: 0 5rem;
  }
}
@media (min-width: 993px) and (max-width: 1699px) {
  .container {
    margin: auto 5rem;
    padding: 0 3rem;
  }
}
@media (max-width: 992px) {
  .container {
    margin: auto 3rem;
    padding: 0 2rem;
  }
}
@media (max-width: 767px) {
  .container {
    margin: auto 2rem;
    padding: 0 2rem;
  }
}
@media (max-width: 576px) {
  .container {
    margin: auto 1.6rem;
    padding: 0 1.6rem;
  }
}

.container-grid-section {
  margin: 0 auto;
  height: 100%;
  min-height: inherit;
  border-left: 1px solid var(--borders__on-dark);
  border-right: 1px solid var(--borders__on-dark);
  position: relative;
  z-index: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
@media (min-width: 1700px) {
  .container-grid-section {
    margin: auto 8rem;
  }
}
@media (min-width: 993px) and (max-width: 1699px) {
  .container-grid-section {
    margin: auto 5rem;
  }
}
@media (max-width: 992px) {
  .container-grid-section {
    margin: auto 3rem;
  }
}
@media (max-width: 767px) {
  .container-grid-section {
    margin: auto 2rem;
  }
}
@media (max-width: 576px) {
  .container-grid-section {
    margin: auto 1.6rem;
  }
}

.container-no-border {
  margin: 0 auto;
}
@media (min-width: 1700px) {
  .container-no-border {
    margin: auto 8rem;
    padding: 0 5rem;
  }
}
@media (min-width: 993px) and (max-width: 1699px) {
  .container-no-border {
    margin: auto 5rem;
    padding: 0 3rem;
  }
}
@media (max-width: 992px) {
  .container-no-border {
    margin: auto 3rem;
    padding: 0 2rem;
  }
}
@media (max-width: 767px) {
  .container-no-border {
    margin: auto 2rem;
    padding: 0 2rem;
  }
}
@media (max-width: 576px) {
  .container-no-border {
    margin: auto 1.6rem;
    padding: 0 1.6rem;
  }
}

.container-inside-container {
  position: relative;
}
@media (min-width: 1700px) {
  .container-inside-container {
    padding: 0 5rem;
  }
}
@media (min-width: 993px) and (max-width: 1699px) {
  .container-inside-container {
    padding: 0 3rem;
  }
}
@media (max-width: 992px) {
  .container-inside-container {
    padding: 0 2rem;
  }
}
@media (max-width: 767px) {
  .container-inside-container {
    padding: 0 2rem;
  }
}
@media (max-width: 576px) {
  .container-inside-container {
    padding: 0 1.6rem;
  }
}

.lines {
  pointer-events: none;
  position: absolute;
  z-index: -5;
  background: transparent;
  width: 100%;
  height: 100%;
  opacity: 1;
  display: flex;
  flex-grow: 0;
  flex-shrink: 0;
  flex-wrap: nowrap;
}
.lines__empty {
  width: calc(25% - 1px);
  background: transparent;
  height: 100%;
  opacity: 0.1;
}
@media (max-width: 992px) {
  .lines__empty {
    width: calc(33.33333% - 1px);
  }
}
.lines__line {
  width: 1px;
  background-color: var(--borders__on-dark);
  height: 100%;
}
.lines__line--invisible {
  width: 1px;
  background-color: transparent;
  height: 100%;
}

.lines-md-2 {
  pointer-events: none;
  position: absolute;
  z-index: -5;
  background: transparent;
  width: 100%;
  height: 100%;
  opacity: 1;
  display: flex;
  flex-grow: 0;
  flex-shrink: 0;
  flex-wrap: nowrap;
}
.lines-md-2__empty {
  width: calc(25% - 1px);
  background: transparent;
  height: 100%;
  opacity: 0.1;
}
@media (max-width: 992px) {
  .lines-md-2__empty {
    width: calc(50% - 1px);
  }
}
.lines-md-2__line {
  width: 1px;
  background-color: var(--borders__on-dark);
  height: 100%;
}

.main-menu-open .container {
  border-left: 1px solid rgba(255, 0, 0, 0);
  border-right: 1px solid rgba(255, 0, 0, 0);
}

.disable-padding {
  padding: 0 !important;
}

.borders-l-r {
  border-left: 1px solid var(--borders__on-dark);
  border-right: 1px solid var(--borders__on-dark);
}

.borders-t {
  border-top: 1px solid var(--borders__on-dark);
}

.borders-b {
  border-bottom: 1px solid var(--borders__on-dark);
}

.borders-t-b {
  border-top: 1px solid var(--borders__on-dark);
  border-bottom: 1px solid var(--borders__on-dark);
}

.borders-all {
  border: 1px solid var(--borders__on-dark);
}

.disable-borders {
  border: none !important;
}

.flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.flex_wrap {
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.flex_aic {
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.flex_aife {
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: flex-end;
}

.flex_jcc {
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.flex_jcsb {
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
}

.flex_row {
  flex-direction: row;
}

@media (max-width: 767px) {
  .flex_mobile-column {
    flex-direction: column;
  }
}
.container-right {
  /* overflow-x: hidden; */
  padding-left: calc((100% - var(--container-width)) / 2 + var(--container-padding));
}
@media (max-width: var(--laptop-size)) {
  .container-right {
    padding-left: var(--container-padding);
  }
}

.grid-section__content {
  font-size: 2.5rem;
  line-height: 140%;
}

.container-left {
  /* overflow-x: hidden; */
  padding-right: calc((100% - var(--container-width)) / 2 + var(--container-padding));
}
@media (max-width: var(--laptop-size)) {
  .container-left {
    padding-right: var(--container-padding);
  }
}

.mt-auto {
  margin-top: auto;
}

.mt-50 {
  margin-top: 5rem;
}

.pt-50 {
  padding-top: 5rem;
}
@media (max-width: 992px) {
  .pt-50 {
    padding-bottom: 4rem;
  }
}
@media (max-width: 767px) {
  .pt-50 {
    padding-bottom: 2rem;
  }
}
@media (max-width: 567px) {
  .pt-50 {
    padding-bottom: 1.6rem;
  }
}

.pb-50 {
  padding-bottom: 5rem;
}
@media (max-width: 992px) {
  .pb-50 {
    padding-bottom: 4rem;
  }
}
@media (max-width: 767px) {
  .pb-50 {
    padding-bottom: 2rem;
  }
}
@media (max-width: 567px) {
  .pb-50 {
    padding-bottom: 1.6rem;
  }
}

.pb-30 {
  padding-bottom: 3rem;
}
@media (max-width: 992px) {
  .pb-30 {
    padding-bottom: 3rem;
  }
}
@media (max-width: 767px) {
  .pb-30 {
    padding-bottom: 2rem;
  }
}
@media (max-width: 567px) {
  .pb-30 {
    padding-bottom: 1.6rem;
  }
}

.gap-3 {
  gap: 3rem;
}

.mb-3 {
  margin-bottom: 3rem;
}
@media (max-width: 992px) {
  .mb-3 {
    margin-bottom: 2rem;
  }
}
@media (max-width: 567px) {
  .mb-3 {
    margin-bottom: 1.6rem;
  }
}

.spares-block ul {
  margin-bottom: 1rem;
}

.spares-block ul li {
  list-style: disc;
  list-style-position: inside;
  padding-bottom: 1rem;
  line-height: 150%;
}

.mw770 {
  max-width: 77rem;
}
@media (max-width: 992px) {
  .mw770 {
    max-width: 100%;
  }
}
.m-0-auto {
  margin: 0 auto;
}

html, body {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.footer {
  margin-top: auto;
}

.footer {
  padding: 60px 0;
  background-color: #e3e3e3;
}

/* Blocks */
.footer {
  background-color: rgb(39, 39, 39);
  padding: 50px 0;
  font-size: 32px;
  color: #fff;
}
.footer h1 {
  font-size: 32px;
}
.footer a {
  color: #fff;
  text-decoration: underline;
}
@media (max-width: 1200px) {
  .footer {
    font-size: 26px;
  }
}

.footer__copyright {
  padding: 10px 0;
  font-size: 16px;
}

.header {
  position: relative;
}

.main-header__section {
  position: relative;
}

@media (max-width: 1220px) {
  .header__nav {
    display: none;
  }
}

.sub-header__section {
  border-bottom: 0.1rem solid var(--borders-color);
}

.sub-header {
  text-transform: uppercase;
  font-weight: var(--font-weight-semibold);
  font-size: 1.3rem;
  color: var(--text-color);
  padding: 1rem 0;
}
.sub-header a {
  color: var(--text-color);
  transition: color 0.3s ease;
  cursor: pointer;
}
.sub-header a:hover {
  color: var(--accent-color);
}
.sub-header a:hover .sub-header__icon-svg {
  color: var(--accent-color);
}

.sub-header__socials {
  gap: 2rem;
}
@media (max-width: 900px) {
  .sub-header__socials {
    gap: 0.5rem 2rem;
  }
}
@media (min-width: 1200px) {
  .sub-header__socials {
    gap: 4rem;
  }
}

.sub-header__calculate-phone {
  gap: 2rem;
  justify-content: end;
}
@media (max-width: 900px) {
  .sub-header__calculate-phone {
    gap: 0.5rem 2rem;
  }
}
@media (min-width: 1200px) {
  .sub-header__calculate-phone {
    gap: 4rem;
  }
}

.sub-header__item {
  display: inline-flex;
  align-items: center;
  gap: 0.8rem;
  color: var(--text-color);
  transition: color 0.3s ease;
}
.sub-header__item:hover {
  color: var(--accent-color);
}
.sub-header__item:hover .sub-header__item-svg {
  color: var(--accent-color);
}

.sub-header__item-svg {
  width: 2rem;
  height: 2rem;
  color: var(--text-color);
  transition: color 0.3s ease;
  flex-shrink: 0;
}
@media (max-width: 460px) {
  .sub-header__item-svg {
    display: none;
  }
}

@media (max-width: 420px) {
  .sub-header__item-email,
  .sub-header__item-calculate {
    display: none;
  }
}
.phone {
  font-weight: var(--font-weight-bold);
}

.main-header__section {
  border-bottom: 0.1rem solid var(--borders-color);
}

.main-header {
  align-items: center;
  font-size: 1.3rem;
  color: var(--text-color);
  padding: 1rem 0;
}
@media (max-width: 719px) {
  .main-header {
    flex-direction: column;
  }
}

.logo-descriptor {
  gap: 3rem;
}
@media (max-width: 576px) {
  .logo-descriptor {
    gap: 5.5rem;
  }
}

.logo {
  width: 17.2rem;
  height: 8.6rem;
}

.descriptor {
  text-transform: none;
  font-weight: var(--font-weight-regular);
  max-width: 11rem;
  font-size: 1.3rem;
  color: var(--grey-text);
  padding-top: 0.7rem;
  margin-right: 2rem;
}

.main-menu {
  gap: 5rem;
}
@media (max-width: 719px) {
  .main-menu {
    margin-top: 2rem;
  }
}
@media (max-width: 1310px) {
  .main-menu {
    gap: 2rem;
  }
}

.menu-btn {
  text-transform: uppercase;
  font-weight: var(--font-weight-semibold);
  background: var(--alternate-accent-color);
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  color: var(--white-text);
  border-radius: 7px;
  padding: 1.3rem 1.2rem;
  width: 17rem;
  height: 5rem;
  gap: 0.8rem;
  flex-wrap: nowrap;
  white-space: nowrap;
  position: relative;
  overflow: hidden;
  flex-shrink: 0;
}
.menu-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 50%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transform: skewX(-30deg);
  animation: shine 7s infinite;
  pointer-events: none;
}
.menu-btn:hover {
  background: linear-gradient(180deg, #3cb5f1 0%, #15a0e5 100%);
}
.menu-btn__icon {
  transition: transform 0.3s ease;
}
.menu-btn--active .menu-btn__icon {
  transform: rotate(90deg) scale(0);
}
@media (max-width: 380px) {
  .menu-btn__icon {
    width: 2rem;
  }
}
.menu-btn__close {
  position: absolute;
  top: 50%;
  left: 1.4rem;
  transform: translateY(-50%) rotate(-90deg) scale(0);
  transition: transform 0.3s ease;
  width: 2.4rem;
  height: 2.4rem;
  flex-shrink: 0;
}
.menu-btn--active .menu-btn__close {
  transform: translateY(-50%) rotate(0deg) scale(1);
}
@media (max-width: 380px) {
  .menu-btn {
    padding: 1.3rem 1rem;
    width: 15rem;
    height: 4rem;
    gap: 0.5rem;
    font-size: 1.2rem;
  }
}

@keyframes shine {
  0% {
    left: -100%;
  }
  20% {
    left: 100%;
  }
  100% {
    left: 100%;
  }
}
.main-menu__list {
  font-size: 1.5rem;
  gap: 3rem;
  text-transform: uppercase;
  font-weight: var(--font-weight-semibold);
}
.main-menu__list a {
  color: var(--black-text);
  border-bottom: 0.2rem solid transparent;
  padding-bottom: 0.5rem;
  transition: all 0.3s ease;
}
.main-menu__list a:hover {
  color: var(--accent-color);
  border-bottom: 0.2rem solid var(--accent-color);
  padding-bottom: 0.5rem;
}
@media (min-width: 963px) and (max-width: 1549px) {
  .main-menu__list {
    gap: 1.6rem;
  }
}
@media (min-width: 1550px) and (max-width: 1700px) {
  .main-menu__list {
    gap: 4rem;
  }
}
@media (min-width: 1701px) and (max-width: 1920px) {
  .main-menu__list {
    gap: 5.5rem;
  }
}
@media (min-width: 1921px) {
  .main-menu__list {
    gap: 10rem;
  }
}
@media (max-width: 1279px) {
  .main-menu__list {
    display: none;
  }
}

.menu-btn--sections {
  background: var(--accent-color);
}
@media (min-width: 1280px) {
  .menu-btn--sections {
    display: none;
  }
}
.menu-btn--sections:hover, .menu-btn--sections:focus {
  background: linear-gradient(180deg, #ffaa78 0%, #fd823b 100%);
}

.menu-overlay {
  position: absolute;
  top: 101%;
  left: 0;
  width: 100%;
  background: var(--white-bg, #ffffff);
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.3s ease;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  max-height: 100vh;
  overflow-y: auto;
}
.menu-overlay.active {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}
.menu-overlay__content {
  padding: 3rem 0;
}

.catalog-menu h3 {
  font-size: 2.4rem;
  font-weight: var(--font-weight-bold);
  color: var(--black-text);
  margin-bottom: 2rem;
}
.catalog-menu__content {
  padding: 2rem 0;
  color: var(--text-color);
  font-size: 1.6rem;
}
.catalog-menu__content p {
  color: var(--grey-text);
  font-style: italic;
}

.sections-menu h3 {
  font-size: 2.4rem;
  font-weight: var(--font-weight-bold);
  color: var(--black-text);
  margin-bottom: 2rem;
}
.sections-menu__list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}
.sections-menu__list li a {
  display: block;
  padding: 1.2rem 0;
  color: var(--black-text);
  text-decoration: none;
  font-size: 1.8rem;
  font-weight: var(--font-weight-medium);
  border-bottom: 1px solid var(--border-color, #e0e0e0);
  transition: all 0.3s ease;
}
.sections-menu__list li a:hover {
  color: var(--accent-color);
  padding-left: 1rem;
}

@media (max-width: 1400px) {
  .hide-on-1400 {
    display: none;
  }
}
.icons-wrapper {
  padding: 30px 0;
  display: flex;
  column-gap: 30px;
}

.icon {
  fill: transparent;
  stroke: transparent;
  width: 62px;
  height: 62px;
}

.icon--heart-line {
  fill: rgb(241, 68, 131);
}

.icon--id-card-line {
  fill: rgb(51, 51, 51);
}

.icon--search-line {
  fill: rgb(28, 176, 80);
}

.icon--user-star {
  fill: rgb(26, 134, 235);
}

.icon--user {
  stroke: rgb(26, 134, 235);
  transition: all 0.2s ease-in;
}
.icon--user:hover {
  stroke: rgb(17, 193, 90);
}

/* Nav Icon */
.mobile-nav-btn {
  --time: 0.1s;
  --width: 40px;
  --height: 30px;
  --line-height: 4px;
  --spacing: 6px;
  --color: #000;
  --radius: 4px;
  /* Fixed height and width */
  /* height: var(--height); */
  /* width: var(--width); */
  /* Dynamic height and width */
  height: calc(var(--line-height) * 3 + var(--spacing) * 2);
  width: var(--width);
  display: flex;
  justify-content: center;
  align-items: center;
}

.nav-icon {
  position: relative;
  width: var(--width);
  height: var(--line-height);
  background-color: var(--color);
  border-radius: var(--radius);
}

.nav-icon::before,
.nav-icon::after {
  content: "";
  display: block;
  position: absolute;
  left: 0;
  width: var(--width);
  height: var(--line-height);
  border-radius: var(--radius);
  background-color: var(--color);
  transition: transform var(--time) ease-in, top var(--time) linear var(--time);
}

.nav-icon::before {
  /* top: calc(var(--line-height) * -2); */
  top: calc(-1 * (var(--line-height) + var(--spacing)));
}

.nav-icon::after {
  /* top: calc(var(--line-height) * 2); */
  top: calc(var(--line-height) + var(--spacing));
}

.nav-icon.nav-icon--active {
  background-color: transparent;
}

.nav-icon.nav-icon--active::before,
.nav-icon.nav-icon--active::after {
  top: 0;
  transition: top var(--time) linear, transform var(--time) ease-in var(--time);
}

.nav-icon.nav-icon--active::before {
  transform: rotate(45deg);
}

.nav-icon.nav-icon--active::after {
  transform: rotate(-45deg);
}

/* Layout */
.mobile-nav-btn {
  z-index: 999;
}

.nav {
  font-size: 18px;
}

.nav__list {
  display: flex;
  column-gap: 30px;
}

.title-1 {
  margin: 1em 0 0.5em;
  font-size: 38px;
  font-weight: 700;
  font-family: var(--font-titles);
}

.title-2 {
  margin: 1em 0 0.5em;
  font-size: 32px;
  font-weight: 700;
  font-family: var(--font-titles);
}

.title-3 {
  margin: 1em 0 0.5em;
  font-size: 26px;
  font-weight: 700;
  font-family: var(--font-titles);
}

.title-4 {
  margin: 1em 0 0.5em;
  font-size: 18px;
  font-weight: 700;
  font-family: var(--font-titles);
}

/* No styles code below. Only in modules */
/* Не пишите CSS код ниже. Только в подключаемых файлах */
/*# sourceMappingURL=data:application/json;charset=utf8;base64,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 */
