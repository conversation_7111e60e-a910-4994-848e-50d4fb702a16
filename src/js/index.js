// Галерея и лайтбоксы от Fancybox
import { Fancybox } from '@fancyapps/ui';
import '@fancyapps/ui/dist/fancybox/fancybox.css';

Fancybox.bind('[data-fancybox]', {
	// Your custom options
});

// Мобильная навигация удалена

// Кнопка "Каталог услуг"
const catalogBtn = document.getElementById('catalogBtn');
const catalogOverlay = document.getElementById('catalogOverlay');

// Кнопка "Разделы сайта"
const sectionsBtn = document.getElementById('sectionsBtn');
const sectionsOverlay = document.getElementById('sectionsOverlay');

console.log('Menu elements:', { catalogBtn, catalogOverlay, sectionsBtn, sectionsOverlay });

// Функция для блокировки/разблокировки скролла
function toggleBodyScroll(disable) {
    if (disable) {
        document.body.style.overflow = 'hidden';
    } else {
        document.body.style.overflow = '';
    }
}

// Функция для закрытия всех меню
function closeAllMenus() {
    catalogOverlay?.classList.remove('active');
    sectionsOverlay?.classList.remove('active');
    catalogBtn?.classList.remove('menu-btn--active');
    sectionsBtn?.classList.remove('menu-btn--active');

    // Разблокируем скролл
    toggleBodyScroll(false);
}

// Обработчик для кнопки "Каталог услуг"
if (catalogBtn && catalogOverlay) {
    catalogBtn.addEventListener('click', function() {
        console.log('Catalog button clicked!');

        // Если меню уже открыто - закрываем
        if (catalogOverlay.classList.contains('active')) {
            closeAllMenus();
        } else {
            // Закрываем другие меню и открываем каталог
            closeAllMenus();
            catalogOverlay.classList.add('active');
            catalogBtn.classList.add('menu-btn--active');

            // Блокируем скролл
            toggleBodyScroll(true);
        }
    });
}

// Обработчик для кнопки "Разделы сайта"
if (sectionsBtn && sectionsOverlay) {
    sectionsBtn.addEventListener('click', function() {
        console.log('Sections button clicked!');

        // Если меню уже открыто - закрываем
        if (sectionsOverlay.classList.contains('active')) {
            closeAllMenus();
        } else {
            // Закрываем другие меню и открываем разделы
            closeAllMenus();
            sectionsOverlay.classList.add('active');
            sectionsBtn.classList.add('menu-btn--active');

            // Блокируем скролл
            toggleBodyScroll(true);
        }
    });
}

// Закрытие меню при клике вне меню (на документе)
document.addEventListener('click', function(e) {
    // Проверяем, что клик был не по кнопкам меню и не внутри выпадающих меню
    if (!catalogBtn?.contains(e.target) &&
        !sectionsBtn?.contains(e.target) &&
        !catalogOverlay?.contains(e.target) &&
        !sectionsOverlay?.contains(e.target)) {
        closeAllMenus();
    }
});

// Закрытие меню при нажатии Escape
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeAllMenus();
    }
});