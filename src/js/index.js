// Галерея и лайтбоксы от Fancybox
import { Fancybox } from '@fancyapps/ui';
import '@fancyapps/ui/dist/fancybox/fancybox.css';

Fancybox.bind('[data-fancybox]', {
	// Your custom options
});

// Мобильная навигация удалена

// Кнопка "Каталог услуг" (только для десктопа ≥1280px)
const catalogBtn = document.getElementById('catalogBtn');
const catalogOverlay = document.getElementById('catalogOverlay');

// Универсальная кнопка меню (для планшетов и мобильных <1280px)
const universalMenuBtn = document.getElementById('universalMenuBtn');
const universalOverlay = document.getElementById('universalOverlay');

console.log('Menu elements:', { catalogBtn, catalogOverlay, universalMenuBtn, universalOverlay });

// Функция для блокировки/разблокировки скролла
function toggleBodyScroll(disable) {
    if (disable) {
        document.body.style.overflow = 'hidden';
    } else {
        document.body.style.overflow = '';
    }
}

// Функция для закрытия всех меню
function closeAllMenus() {
    catalogOverlay?.classList.remove('active');
    universalOverlay?.classList.remove('active');
    catalogBtn?.classList.remove('menu-btn--active');
    universalMenuBtn?.classList.remove('menu-btn--active');

    // Разблокируем скролл
    toggleBodyScroll(false);
}

// Обработчик для кнопки "Каталог услуг" (только для десктопа)
if (catalogBtn && catalogOverlay) {
    catalogBtn.addEventListener('click', function() {
        console.log('Catalog button clicked!');

        // Если меню уже открыто - закрываем
        if (catalogOverlay.classList.contains('active')) {
            closeAllMenus();
        } else {
            // Закрываем другие меню и открываем каталог
            closeAllMenus();
            catalogOverlay.classList.add('active');
            catalogBtn.classList.add('menu-btn--active');

            // Блокируем скролл
            toggleBodyScroll(true);
        }
    });
}

// Обработчик для универсальной кнопки меню (планшеты и мобильные)
if (universalMenuBtn && universalOverlay) {
    universalMenuBtn.addEventListener('click', function() {
        console.log('Universal menu button clicked!');

        // Если меню уже открыто - закрываем
        if (universalOverlay.classList.contains('active')) {
            closeAllMenus();
        } else {
            // Закрываем другие меню и открываем универсальное
            closeAllMenus();
            universalOverlay.classList.add('active');
            universalMenuBtn.classList.add('menu-btn--active');

            // Блокируем скролл
            toggleBodyScroll(true);
        }
    });
}

// Закрытие меню при клике вне меню (на документе)
document.addEventListener('click', function(e) {
    // Проверяем, что клик был не по кнопкам меню и не внутри выпадающих меню
    if (!catalogBtn?.contains(e.target) &&
        !universalMenuBtn?.contains(e.target) &&
        !catalogOverlay?.contains(e.target) &&
        !universalOverlay?.contains(e.target)) {
        closeAllMenus();
    }
});

// Закрытие меню при нажатии Escape
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeAllMenus();
    }
});