.header {
}

.header__nav {
	@include tablet {
		display: none;
	}
}
.sub-header {
	display: flex;
	flex-direction: row;
	justify-content: space-between;
	text-transform: uppercase;
	font-weight: var(--font-weight-semibold);
	font-size:1.3rem;
	color: var(--text-color);
	padding: 1rem 0;
	border-bottom: .1rem solid var(--borders-color);
	
	& a {
	color: var(--text-color);
	}
	& a:hover {
		color: var(--accent-color);
	}
	
}
.sub-header__socials {
}
.sub-header__icon {
	background-image: url(./../img/svgicons/whatsapp.svg);
	background-size: 2.4rem 2.4rem;
	background-position: left;
	padding-left:3rem;
}
.whatsapp {
}
.telegram {
}
.email {
}
.sub-header__calculate-phone {
}
.calculate {
}
.phone {
	font-weight: var(--font-weight-bold);
}