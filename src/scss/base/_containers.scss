.container {
	margin: 0 auto;
	border-left:1px solid var(--borders__on-dark);
	border-right:1px solid var(--borders__on-dark);

	@media (min-width: 1700px) {
		margin:auto 8rem;
		padding: 0 5rem;
	}
	@media (min-width: 993px) and (max-width:1699px) {
		margin:auto 5rem;
		padding: 0 3rem;
	}
	@media (max-width: 992px) {
		margin:auto 3rem;
		padding: 0 2rem;
	}
	@media (max-width: 767px) {
		margin:auto 2rem;
		padding: 0 2rem;
	}
	@media (max-width: 576px) {
		margin:auto 1.6rem;
		padding: 0 1.6rem;
	}
}

.container-grid-section {
	margin: 0 auto;
	height: 100%;
	min-height: inherit;
	border-left:1px solid var(--borders__on-dark);
	border-right:1px solid var(--borders__on-dark);
	position: relative;
	z-index: 1;
	overflow: hidden;

	display: flex;
    flex-direction: column;
    justify-content: space-between;

	@media (min-width: 1700px) {
		margin:auto 8rem;
	}
	@media (min-width: 993px) and (max-width:1699px) {
		margin:auto 5rem;
	}
	@media (max-width: 992px) {
		margin:auto 3rem;
	}
	@media (max-width: 767px) {
		margin:auto 2rem;
	}
	@media (max-width: 576px) {
		margin:auto 1.6rem;
	}
}

.container-no-border {
	margin: 0 auto;

	@media (min-width: 1700px) {
		margin:auto 8rem;
		padding: 0 5rem;
	}
	@media (min-width: 993px) and (max-width:1699px) {
		margin:auto 5rem;
		padding: 0 3rem;
	}
	@media (max-width: 992px) {
		margin:auto 3rem;
		padding: 0 2rem;
	}
	@media (max-width: 767px) {
		margin:auto 2rem;
		padding: 0 2rem;
	}
	@media (max-width: 576px) {
		margin:auto 1.6rem;
		padding: 0 1.6rem;
	}
}

.container-inside-container {
	position: relative;

	@media (min-width: 1700px) {
		padding: 0 5rem;
	}
	@media (min-width: 993px) and (max-width:1699px) {
		padding: 0 3rem;
	}
	@media (max-width: 992px) {
		padding: 0 2rem;
	}
	@media (max-width: 767px) {
		padding: 0 2rem;
	}
	@media (max-width: 576px) {
		padding: 0 1.6rem;
	}
}

.lines {
	pointer-events: none;
	position: absolute;
	z-index: -5;
    background: transparent;
    width: 100%;
    height: 100%;
    opacity: 1;
    display: flex;
    flex-grow: 0;
    flex-shrink: 0;
    flex-wrap: nowrap;

    &__empty{
        width: calc(25% - 1px);
        background: transparent;
        height: 100%;
        opacity: .1;

        @media (max-width: 992px) {
            width: calc(33.33333% - 1px);
        }
    }
    &__line {
        width: 1px;
        background-color: var(--borders__on-dark);
        height: 100%;
    }

	&__line--invisible {
		width: 1px;
        background-color: transparent;
        height: 100%;
	}
}


.lines-md-2 {
	pointer-events: none;
	position: absolute;
	z-index: -5;
    background: transparent;
    width: 100%;
    height: 100%;
    opacity: 1;
    display: flex;
    flex-grow: 0;
    flex-shrink: 0;
    flex-wrap: nowrap;

    &__empty{
        width: calc(25% - 1px);
        background: transparent;
        height: 100%;
        opacity: .1;

        @media (max-width: 992px) {
            width: calc(50% - 1px);
        }
    }
    &__line {
        width: 1px;
        background-color: var(--borders__on-dark);
        height: 100%;
    }
}

.main-menu-open .container {
	border-left: 1px solid rgba(255, 0, 0, 0);
	border-right: 1px solid rgba(255, 0, 0, 0);
}
.main-menu-open .container {

}
.disable-padding {
	padding: 0 !important;
}
.borders-l-r {
	border-left:1px solid var(--borders__on-dark);
	border-right:1px solid var(--borders__on-dark);
}
.borders-t {
	border-top:1px solid var(--borders__on-dark);
}
.borders-b {
	border-bottom:1px solid var(--borders__on-dark);
}
.borders-t-b {
	border-top:1px solid var(--borders__on-dark);
	border-bottom:1px solid var(--borders__on-dark);
}
.borders-all {
	border:1px solid var(--borders__on-dark);
}
.disable-borders {
	border: none !important;
}



.flex {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex
}

.flex_fw {
    -ms-flex-wrap: wrap;
    flex-wrap: wrap
}

.flex_aic {
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.flex_aife {
    -webkit-box-align: end;
    -ms-flex-align: end;
    align-items: flex-end
}

.flex_jcc {
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center
}

.flex_jcsb {
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between
}
@media (max-width:767px) {
.flex_mobile-column {
	flex-direction: column;
}
}



.container-right {
	/* overflow-x: hidden; */
	padding-left: calc(
		(100% - var(--container-width)) / 2 + var(--container-padding)
	);

	@media (max-width: var(--laptop-size)) {
		padding-left: var(--container-padding);
	}
}

.grid-section__content {
	font-size: 2.5rem;
	line-height: 140%;
}

.container-left {
	/* overflow-x: hidden; */
	padding-right: calc(
		(100% - var(--container-width)) / 2 + var(--container-padding)
	);

	@media (max-width: var(--laptop-size)) {
		padding-right: var(--container-padding);
	}
}

.mt-auto {
    margin-top: auto
}

.mt-50 {
	margin-top:5rem;
}
.pt-50 {
	padding-top:5rem;
	@media (max-width: 992px) {
		padding-bottom: 4rem;
	}
	@media (max-width: 767px) {
		padding-bottom: 2rem;
	}
	@media (max-width: 567px) {
		padding-bottom: 1.6rem;
	}
}
.pb-50 {
	padding-bottom:5rem;
	@media (max-width: 992px) {
		padding-bottom: 4rem;
	}
	@media (max-width: 767px) {
		padding-bottom: 2rem;
	}
	@media (max-width: 567px) {
		padding-bottom: 1.6rem;
	}
}
.pb-30 {
	padding-bottom:3rem;
	@media (max-width: 992px) {
		padding-bottom: 3rem;
	}
	@media (max-width: 767px) {
		padding-bottom: 2rem;
	}
	@media (max-width: 567px) {
		padding-bottom: 1.6rem;
	}
}
.gap-3 {
	gap:3rem;
}
.mb-3 {
	margin-bottom: 3rem;

	@media (max-width: 992px) {
		margin-bottom: 2rem;
	}
	@media (max-width: 767px) {
		
	}
	@media (max-width: 567px) {
		margin-bottom: 1.6rem;
	}
}

.spares-block ul {
	margin-bottom: 1rem;
}
.spares-block ul li {
	list-style: disc;
    list-style-position: inside;
    padding-bottom: 1rem;
    line-height: 150%;
}
.spares-block ul li a {}

.mw770 {
	max-width: 77rem;

	@media (max-width:992px) {max-width: 100%;}
	@media (max-width:767px) {}
}
.m-0-auto {
	margin: 0 auto;
}