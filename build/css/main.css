@charset "UTF-8";
/* Base */ /* Reset and base styles  */
* {
  padding: 0px;
  margin: 0px;
  border: none;
}

*,
*::before,
*::after {
  box-sizing: border-box;
}

/* Links */
a, a:link, a:visited {
  text-decoration: none;
}

a:hover {
  text-decoration: none;
}

/* Common */
aside, nav, footer, header, section, main {
  display: block;
}

h1, h2, h3, h4, h5, h6, p {
  font-size: inherit;
  font-weight: inherit;
}

ul, ul li {
  list-style: none;
}

img {
  vertical-align: top;
}

img, svg {
  max-width: 100%;
  height: auto;
}

address {
  font-style: normal;
}

/* Form */
input, textarea, button, select {
  font-family: inherit;
  font-size: inherit;
  color: inherit;
  background-color: transparent;
}

input::-ms-clear {
  display: none;
}

button, input[type=submit] {
  display: inline-block;
  box-shadow: none;
  background-color: transparent;
  background: none;
  cursor: pointer;
}

input:focus, input:active,
button:focus, button:active {
  outline: none;
}

button::-moz-focus-inner {
  padding: 0;
  border: 0;
}

label {
  cursor: pointer;
}

legend {
  display: block;
}

:root {
  --container-width: 1200px;
  --container-padding: 15px;
  --font-main: "Onest", sans-serif;
  --font-accent: "Manrope", sans-serif;
  --font-titles: var(--font-main);
  --font-legacy: "mainfont";
  --font-weight-thin: 100;
  --font-weight-extralight: 200;
  --font-weight-light: 300;
  --font-weight-regular: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;
  --font-weight-black: 900;
  --font-thin: var(--font-main);
  --font-extralight: var(--font-main);
  --font-light: var(--font-main);
  --font-regular: var(--font-main);
  --font-medium: var(--font-main);
  --font-semibold: var(--font-main);
  --font-bold: var(--font-main);
  --font-extrabold: var(--font-main);
  --font-black: var(--font-main);
  --page-bg: #fff;
  --text-color: #000;
  --accent: #ac182c;
  --link-color: #2578c8;
  --laptop-size: 1199px;
  --tablet-size: 959px;
  --mobile-size: 599px;
  --accent-color: #FF752B;
  --alternate-accent-color: #15A0E5;
  --grey-text: #707070;
  --white-text: #FFF;
  --black-text: #282828;
  --borders-color: rgba(178,188,195,.5);
}

.dark {
  --page-bg: #252526;
  --text-color: #fff;
}

@font-face {
  font-family: FirasansBook;
  font-display: swap;
  src: url("../fonts/FirasansBook.woff2") format("woff2"), url("../fonts/FirasansBook.woff") format("woff");
  font-weight: 400;
  font-style: normal;
}
@font-face {
  font-family: Montserrat;
  font-display: swap;
  src: url("../fonts/Montserrat-Bold.woff2") format("woff2"), url("../fonts/Montserrat-Bold.woff") format("woff");
  font-weight: 700;
  font-style: normal;
}
@font-face {
  font-family: Montserrat;
  font-display: swap;
  src: url("../fonts/Montserrat-BoldItalic.woff2") format("woff2"), url("../fonts/Montserrat-BoldItalic.woff") format("woff");
  font-weight: 400;
  font-style: normal;
}
@font-face {
  font-family: Montserrat;
  font-display: swap;
  src: url("../fonts/Montserrat-Regular.woff2") format("woff2"), url("../fonts/Montserrat-Regular.woff") format("woff");
  font-weight: 400;
  font-style: normal;
}
html {
  font-size: 62.5%;
  height: 100%;
  width: 100%;
  scroll-behavior: smooth;
}
@media (max-width: 1500px) {
  html {
    font-size: 62.5%;
  }
}
@media (max-width: 992px) {
  html {
    font-size: 60%;
  }
}
@media (max-width: 767px) {
  html {
    font-size: 58%;
  }
}
@media (max-width: 576px) {
  html {
    font-size: 56%;
  }
}

body {
  background-color: var(--bgr-color__dark-gray-original);
  color: var(--text__on-dark);
  font-family: var(--font-main);
  font-size: 1.6rem;
}
@media (max-width: 767px) {
  body {
    font-size: 1.4rem;
  }
}
@media (max-width: 576px) {
  body {
    font-size: 1.2rem;
  }
}

.btn {
  display: inline-flex;
}

.btn-normal .btn-click {
  -webkit-box-shadow: inset 0px 0px 0px 1px var(--borders__on-dark);
  box-shadow: inset 0px 0px 0px 1px var(--borders__on-dark);
}

.btn-normal .btn-click-white {
  -webkit-box-shadow: inset 0px 0px 0px 1px var(--headers__on-dark);
  box-shadow: inset 0px 0px 0px 1px var(--headers__on-dark);
}

.btn-click {
  cursor: pointer;
  border: 0;
  color: var(--text__on-dark);
  background: transparent;
  border-radius: 10rem !important;
  min-width: 1rem;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  text-decoration: none;
  will-change: transform;
  outline: 0;
  transform: translateZ(0) rotate(0.001deg);
  padding: 1.7rem 3rem !important;
  margin: 0 !important;
  text-decoration: none !important;
  font-size: 1.4rem;
}
@media (max-width: 992px) {
  .btn-click {
    font-size: 1.2rem;
    height: 4.8rem;
    padding: 1.7rem 2rem !important;
  }
}

.btn-click-small-padding {
  padding: 1rem 1.5rem !important;
}

.btn-click-white {
  cursor: pointer;
  border: 0;
  color: var(--headers__on-dark);
  background: transparent;
  border-radius: 10rem !important;
  min-width: 1rem;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  text-decoration: none;
  will-change: transform;
  outline: 0;
  transform: translateZ(0) rotate(0.001deg);
  padding: 1.7rem 3rem !important;
  margin: 0 !important;
  text-decoration: none !important;
  font-size: 1.4rem;
}
@media (max-width: 992px) {
  .btn-click-white {
    font-size: 1.2rem;
  }
}

.btn-bgr-dark {
  background: var(--bgr-color__dark-gray-original);
}

.btn-bgr-white {
  background: var(--headers__on-dark);
}

.btn-fill {
  background: var(--borders__on-dark);
  position: absolute;
  width: 150%;
  height: 200%;
  border-radius: 50%;
  top: -50%;
  left: -25%;
  transform: translate3d(0, -76%, 0);
  will-change: transform;
  transition: background-color ease-in-out 0.25s;
}

.btn-text {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  z-index: 2;
  color: var(--text__on-dark);
  position: relative;
  transform: rotate(0.001deg);
  pointer-events: none;
  will-change: transform, color;
}

.btn-normal .btn-text .btn-text-inner {
  color: var(--headers__on-dark) !important;
  display: flex;
  align-items: center;
  gap: 1rem;
  justify-content: center;
}

.pointernone {
  pointer-events: none;
}

.zindex {
  z-index: 9999999999999;
  pointer-events: all;
}

.btn-text-inner img {
  width: 2.4rem;
}

@media (max-width: 992) {
  .btn-text-inner img {
    width: 1.4rem;
  }
}
@media (min-width: 993) {
  .btn-text-inner img {
    width: 1.5rem;
  }
}
@media (max-width: 1400) {
  .btn-text-inner img {
    width: 1.6rem;
  }
}
.uppercase {
  text-transform: uppercase;
}

.main-button {
  font-size: 1.4rem;
  color: var(--headers__on-dark);
  background: var(--bgr-color__dark-gray-original);
  padding: 0.3rem 0.3rem 0.3rem 2.5rem;
  border: 1px solid var(--headers__on-dark);
  border-radius: 50rem;
  align-items: center;
  height: 5.8rem;
  display: flex;
  justify-content: flex-start;
  position: relative;
  z-index: 1;
}
@media (max-width: 992px) {
  .main-button {
    font-size: 1.2rem;
    padding: 0.3rem 0.3rem 0.3rem 0.9rem;
    height: 4.6rem;
  }
}
.main-button__icon {
  border-radius: 10rem;
  background: linear-gradient(180deg, #FFF 0%, #BABABA 100%);
  box-shadow: -1px 0px 10.3px 0px rgba(0, 0, 0, 0.11);
  width: 5.2rem;
  height: 5.2rem;
  display: flex;
  justify-content: end;
  align-items: center;
  margin-left: 1.5rem;
  position: absolute;
  right: 0.2rem;
  transition: var(--animation-smooth);
  z-index: -1;
}
.main-button__icon img {
  height: 50%;
  padding-right: 1.3rem;
}
@media (max-width: 992px) {
  .main-button__icon img {
    padding-right: 1rem;
  }
}
@media (max-width: 992px) {
  .main-button__icon {
    width: 4rem;
    height: 4rem;
    margin-left: 1rem;
  }
}
.main-button:hover {
  color: var(--borders__on-dark);
}
.main-button:hover .main-button__icon {
  width: calc(100% - 0.4rem);
}
.main-button__dummy {
  visibility: hidden;
  width: 5.2rem;
  height: 5.2rem;
  margin-left: 1.5rem;
}
@media (max-width: 992px) {
  .main-button__dummy {
    width: 4rem;
    height: 4rem;
    margin-left: 1rem;
  }
}

::-moz-selection {
  background-color: var(--borders__on-dark);
  color: var(--headers__on-dark);
}

::selection {
  background-color: var(--borders__on-dark);
  color: var(--headers__on-dark);
}

.docs {
  display: grid;
  line-height: 1.5;
}
.docs p {
  margin: 1rem 0;
}
.docs ul,
.docs ol {
  padding-left: 2rem;
}
.docs ul li,
.docs ol li {
  list-style: disc;
  margin-bottom: 0.5rem;
}
.docs ol li {
  list-style: decimal;
}
.docs section, .docs section.docs {
  padding: 40px 0;
}
.docs section + section {
  border-top: 1px solid #dae5e9;
}
.docs small {
  font-size: 1rem;
  color: rgb(172, 172, 172);
}
.docs .title-1:first-child,
.docs .title-2:first-child {
  margin-top: 0 !important;
}

.test {
  width: 600px;
  height: 300px;
  margin: 50px auto;
  background-color: #999;
  background-position: center center; /* x y */
  background-size: cover;
  background-repeat: no-repeat;
  background-image: url("./../img/project-02.jpg");
}
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .test {
    background-image: url("./../img/<EMAIL>");
  }
}

.test-2 {
  width: 600px;
  height: 300px;
  margin: 50px auto;
  background-color: #999;
  background-position: center center; /* x y */
  background-size: cover;
  background-repeat: no-repeat;
  background-image: image-set(url("./../img/project-02.jpg") 1x, url("./../img/<EMAIL>") 2x);
}

.font-1 {
  font-family: "Montserrat";
  font-weight: 700;
  font-style: italic;
}

.font-2 {
  font-family: "FirasansBook";
  font-weight: 400;
}

/* Отключить при необходимости */
.none {
  display: none !important;
}

.visually-hidden {
  position: absolute;
  width: 1px;
  height: 1px;
  margin: -1px;
  border: 0;
  padding: 0;
  white-space: nowrap;
  clip-path: inset(100%);
  clip: rect(0 0 0 0);
  overflow: hidden;
}

.no-scroll {
  overflow-y: hidden;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.text-center {
  text-align: center;
}

.d-flex {
  display: flex;
}

.flex-center {
  justify-content: center;
}

.container {
  margin: 0 auto;
  border-left: 1px solid var(--borders__on-dark);
  border-right: 1px solid var(--borders__on-dark);
}
@media (min-width: 1700px) {
  .container {
    margin: auto 8rem;
    padding: 0 5rem;
  }
}
@media (min-width: 993px) and (max-width: 1699px) {
  .container {
    margin: auto 5rem;
    padding: 0 3rem;
  }
}
@media (max-width: 992px) {
  .container {
    margin: auto 3rem;
    padding: 0 2rem;
  }
}
@media (max-width: 767px) {
  .container {
    margin: auto 2rem;
    padding: 0 2rem;
  }
}
@media (max-width: 576px) {
  .container {
    margin: auto 1.6rem;
    padding: 0 1.6rem;
  }
}

.container-grid-section {
  margin: 0 auto;
  height: 100%;
  min-height: inherit;
  border-left: 1px solid var(--borders__on-dark);
  border-right: 1px solid var(--borders__on-dark);
  position: relative;
  z-index: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
@media (min-width: 1700px) {
  .container-grid-section {
    margin: auto 8rem;
  }
}
@media (min-width: 993px) and (max-width: 1699px) {
  .container-grid-section {
    margin: auto 5rem;
  }
}
@media (max-width: 992px) {
  .container-grid-section {
    margin: auto 3rem;
  }
}
@media (max-width: 767px) {
  .container-grid-section {
    margin: auto 2rem;
  }
}
@media (max-width: 576px) {
  .container-grid-section {
    margin: auto 1.6rem;
  }
}

.container-no-border {
  margin: 0 auto;
}
@media (min-width: 1700px) {
  .container-no-border {
    margin: auto 8rem;
    padding: 0 5rem;
  }
}
@media (min-width: 993px) and (max-width: 1699px) {
  .container-no-border {
    margin: auto 5rem;
    padding: 0 3rem;
  }
}
@media (max-width: 992px) {
  .container-no-border {
    margin: auto 3rem;
    padding: 0 2rem;
  }
}
@media (max-width: 767px) {
  .container-no-border {
    margin: auto 2rem;
    padding: 0 2rem;
  }
}
@media (max-width: 576px) {
  .container-no-border {
    margin: auto 1.6rem;
    padding: 0 1.6rem;
  }
}

.container-inside-container {
  position: relative;
}
@media (min-width: 1700px) {
  .container-inside-container {
    padding: 0 5rem;
  }
}
@media (min-width: 993px) and (max-width: 1699px) {
  .container-inside-container {
    padding: 0 3rem;
  }
}
@media (max-width: 992px) {
  .container-inside-container {
    padding: 0 2rem;
  }
}
@media (max-width: 767px) {
  .container-inside-container {
    padding: 0 2rem;
  }
}
@media (max-width: 576px) {
  .container-inside-container {
    padding: 0 1.6rem;
  }
}

.lines {
  pointer-events: none;
  position: absolute;
  z-index: -5;
  background: transparent;
  width: 100%;
  height: 100%;
  opacity: 1;
  display: flex;
  flex-grow: 0;
  flex-shrink: 0;
  flex-wrap: nowrap;
}
.lines__empty {
  width: calc(25% - 1px);
  background: transparent;
  height: 100%;
  opacity: 0.1;
}
@media (max-width: 992px) {
  .lines__empty {
    width: calc(33.33333% - 1px);
  }
}
.lines__line {
  width: 1px;
  background-color: var(--borders__on-dark);
  height: 100%;
}
.lines__line--invisible {
  width: 1px;
  background-color: transparent;
  height: 100%;
}

.lines-md-2 {
  pointer-events: none;
  position: absolute;
  z-index: -5;
  background: transparent;
  width: 100%;
  height: 100%;
  opacity: 1;
  display: flex;
  flex-grow: 0;
  flex-shrink: 0;
  flex-wrap: nowrap;
}
.lines-md-2__empty {
  width: calc(25% - 1px);
  background: transparent;
  height: 100%;
  opacity: 0.1;
}
@media (max-width: 992px) {
  .lines-md-2__empty {
    width: calc(50% - 1px);
  }
}
.lines-md-2__line {
  width: 1px;
  background-color: var(--borders__on-dark);
  height: 100%;
}

.main-menu-open .container {
  border-left: 1px solid rgba(255, 0, 0, 0);
  border-right: 1px solid rgba(255, 0, 0, 0);
}

.disable-padding {
  padding: 0 !important;
}

.borders-l-r {
  border-left: 1px solid var(--borders__on-dark);
  border-right: 1px solid var(--borders__on-dark);
}

.borders-t {
  border-top: 1px solid var(--borders__on-dark);
}

.borders-b {
  border-bottom: 1px solid var(--borders__on-dark);
}

.borders-t-b {
  border-top: 1px solid var(--borders__on-dark);
  border-bottom: 1px solid var(--borders__on-dark);
}

.borders-all {
  border: 1px solid var(--borders__on-dark);
}

.disable-borders {
  border: none !important;
}

.flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.flex_wrap {
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.flex_aic {
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.flex_aife {
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: flex-end;
}

.flex_jcc {
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.flex_jcsb {
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
}

.flex_row {
  flex-direction: row;
}

@media (max-width: 767px) {
  .flex_mobile-column {
    flex-direction: column;
  }
}
.container-right {
  /* overflow-x: hidden; */
  padding-left: calc((100% - var(--container-width)) / 2 + var(--container-padding));
}
@media (max-width: var(--laptop-size)) {
  .container-right {
    padding-left: var(--container-padding);
  }
}

.grid-section__content {
  font-size: 2.5rem;
  line-height: 140%;
}

.container-left {
  /* overflow-x: hidden; */
  padding-right: calc((100% - var(--container-width)) / 2 + var(--container-padding));
}
@media (max-width: var(--laptop-size)) {
  .container-left {
    padding-right: var(--container-padding);
  }
}

.mt-auto {
  margin-top: auto;
}

.mt-50 {
  margin-top: 5rem;
}

.pt-50 {
  padding-top: 5rem;
}
@media (max-width: 992px) {
  .pt-50 {
    padding-bottom: 4rem;
  }
}
@media (max-width: 767px) {
  .pt-50 {
    padding-bottom: 2rem;
  }
}
@media (max-width: 567px) {
  .pt-50 {
    padding-bottom: 1.6rem;
  }
}

.pb-50 {
  padding-bottom: 5rem;
}
@media (max-width: 992px) {
  .pb-50 {
    padding-bottom: 4rem;
  }
}
@media (max-width: 767px) {
  .pb-50 {
    padding-bottom: 2rem;
  }
}
@media (max-width: 567px) {
  .pb-50 {
    padding-bottom: 1.6rem;
  }
}

.pb-30 {
  padding-bottom: 3rem;
}
@media (max-width: 992px) {
  .pb-30 {
    padding-bottom: 3rem;
  }
}
@media (max-width: 767px) {
  .pb-30 {
    padding-bottom: 2rem;
  }
}
@media (max-width: 567px) {
  .pb-30 {
    padding-bottom: 1.6rem;
  }
}

.gap-3 {
  gap: 3rem;
}

.mb-3 {
  margin-bottom: 3rem;
}
@media (max-width: 992px) {
  .mb-3 {
    margin-bottom: 2rem;
  }
}
@media (max-width: 567px) {
  .mb-3 {
    margin-bottom: 1.6rem;
  }
}

.spares-block ul {
  margin-bottom: 1rem;
}

.spares-block ul li {
  list-style: disc;
  list-style-position: inside;
  padding-bottom: 1rem;
  line-height: 150%;
}

.mw770 {
  max-width: 77rem;
}
@media (max-width: 992px) {
  .mw770 {
    max-width: 100%;
  }
}
.m-0-auto {
  margin: 0 auto;
}

html, body {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.footer {
  margin-top: auto;
}

.footer {
  padding: 60px 0;
  background-color: #e3e3e3;
}

/* Blocks */
.footer {
  background-color: rgb(39, 39, 39);
  padding: 50px 0;
  font-size: 32px;
  color: #fff;
}
.footer h1 {
  font-size: 32px;
}
.footer a {
  color: #fff;
  text-decoration: underline;
}
@media (max-width: 1200px) {
  .footer {
    font-size: 26px;
  }
}

.footer__copyright {
  padding: 10px 0;
  font-size: 16px;
}

.header {
  position: relative;
}

.main-header__section {
  position: relative;
}

@media (max-width: 1220px) {
  .header__nav {
    display: none;
  }
}

.sub-header__section {
  border-bottom: 0.1rem solid var(--borders-color);
}

.sub-header {
  text-transform: uppercase;
  font-weight: var(--font-weight-semibold);
  font-size: 1.3rem;
  color: var(--text-color);
  padding: 1rem 0;
}
.sub-header a {
  color: var(--text-color);
  transition: color 0.3s ease;
  cursor: pointer;
}
.sub-header a:hover {
  color: var(--accent-color);
}
.sub-header a:hover .sub-header__icon-svg {
  color: var(--accent-color);
}

.sub-header__socials {
  gap: 2rem;
}
@media (max-width: 900px) {
  .sub-header__socials {
    gap: 0.5rem 2rem;
  }
}
@media (min-width: 1200px) {
  .sub-header__socials {
    gap: 4rem;
  }
}

.sub-header__calculate-phone {
  gap: 2rem;
  justify-content: end;
}
@media (max-width: 900px) {
  .sub-header__calculate-phone {
    gap: 0.5rem 2rem;
  }
}
@media (min-width: 1200px) {
  .sub-header__calculate-phone {
    gap: 4rem;
  }
}

.sub-header__item {
  display: inline-flex;
  align-items: center;
  gap: 0.8rem;
  color: var(--text-color);
  transition: color 0.3s ease;
}
.sub-header__item:hover {
  color: var(--accent-color);
}
.sub-header__item:hover .sub-header__item-svg {
  color: var(--accent-color);
}

.sub-header__item-svg {
  width: 2rem;
  height: 2rem;
  color: var(--text-color);
  transition: color 0.3s ease;
  flex-shrink: 0;
}
@media (max-width: 460px) {
  .sub-header__item-svg {
    display: none;
  }
}

@media (max-width: 420px) {
  .sub-header__item-email,
  .sub-header__item-calculate {
    display: none;
  }
}
.phone {
  font-weight: var(--font-weight-bold);
}

.main-header__section {
  border-bottom: 0.1rem solid var(--borders-color);
}

.main-header {
  align-items: center;
  font-size: 1.3rem;
  color: var(--text-color);
  padding: 1rem 0;
}

.logo-descriptor {
  gap: 3rem;
}
@media (max-width: 576px) {
  .logo-descriptor {
    gap: 5.5rem;
  }
}

.logo {
  width: 17.2rem;
  height: 8.6rem;
}

.descriptor {
  text-transform: none;
  font-weight: var(--font-weight-regular);
  max-width: 11rem;
  font-size: 1.3rem;
  color: var(--grey-text);
  padding-top: 0.7rem;
  margin-right: 2rem;
}
@media (max-width: 767px) {
  .descriptor {
    display: none;
  }
}

.main-menu {
  gap: 5rem;
}
@media (max-width: 719px) {
  .main-menu {
    margin-top: 2rem;
  }
}
@media (max-width: 1310px) {
  .main-menu {
    gap: 2rem;
  }
}

.menu-btn {
  text-transform: uppercase;
  font-weight: var(--font-weight-semibold);
  background: var(--alternate-accent-color);
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  color: var(--white-text);
  border-radius: 7px;
  padding: 1.3rem 1.2rem;
  width: 17rem;
  height: 5rem;
  gap: 0.8rem;
  flex-wrap: nowrap;
  white-space: nowrap;
  position: relative;
  overflow: hidden;
  flex-shrink: 0;
}
.menu-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 50%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transform: skewX(-30deg);
  animation: shine 7s infinite;
  pointer-events: none;
}
.menu-btn:hover {
  background: linear-gradient(180deg, #3cb5f1 0%, #15a0e5 100%);
}
.menu-btn__icon {
  transition: transform 0.3s ease;
}
.menu-btn--active .menu-btn__icon {
  transform: rotate(90deg) scale(0);
}
@media (max-width: 380px) {
  .menu-btn__icon {
    width: 2rem;
  }
}
.menu-btn__close {
  position: absolute;
  top: 50%;
  left: 1.4rem;
  transform: translateY(-50%) rotate(-90deg) scale(0);
  transition: transform 0.3s ease;
  width: 2.4rem;
  height: 2.4rem;
  flex-shrink: 0;
}
.menu-btn--active .menu-btn__close {
  transform: translateY(-50%) rotate(0deg) scale(1);
}
@media (max-width: 380px) {
  .menu-btn {
    padding: 1.3rem 1rem;
    width: 15rem;
    height: 4rem;
    gap: 0.5rem;
    font-size: 1.2rem;
  }
}

@keyframes shine {
  0% {
    left: -100%;
  }
  20% {
    left: 100%;
  }
  100% {
    left: 100%;
  }
}
.main-menu__list {
  font-size: 1.5rem;
  gap: 3rem;
  text-transform: uppercase;
  font-weight: var(--font-weight-semibold);
}
.main-menu__list a {
  color: var(--black-text);
  border-bottom: 0.2rem solid transparent;
  padding-bottom: 0.5rem;
  transition: all 0.3s ease;
}
.main-menu__list a:hover {
  color: var(--accent-color);
  border-bottom: 0.2rem solid var(--accent-color);
  padding-bottom: 0.5rem;
}
@media (min-width: 963px) and (max-width: 1549px) {
  .main-menu__list {
    gap: 1.6rem;
  }
}
@media (min-width: 1550px) and (max-width: 1700px) {
  .main-menu__list {
    gap: 4rem;
  }
}
@media (min-width: 1701px) and (max-width: 1920px) {
  .main-menu__list {
    gap: 5.5rem;
  }
}
@media (min-width: 1921px) {
  .main-menu__list {
    gap: 10rem;
  }
}
@media (max-width: 1279px) {
  .main-menu__list {
    display: none;
  }
}

@media (max-width: 1279px) {
  .menu-btn--catalog {
    display: none;
  }
}

.menu-btn--universal {
  width: 6rem;
  height: 6rem;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
}
@media (min-width: 1280px) {
  .menu-btn--universal {
    display: none;
  }
}
@media (min-width: 768px) and (max-width: 1279px) {
  .menu-btn--universal {
    background: var(--light-bg, #f8f9fa);
    border: 1px solid var(--borders-color);
  }
  .menu-btn--universal:hover {
    background: var(--accent-color);
    color: var(--white-text);
  }
}
@media (max-width: 767px) {
  .menu-btn--universal {
    width: auto;
    height: auto;
    flex-direction: row;
    gap: 1rem;
    padding: 1.2rem 2rem;
    background: var(--accent-color);
    color: var(--white-text);
  }
  .menu-btn--universal:hover {
    background: linear-gradient(180deg, #ffaa78 0%, #fd823b 100%);
  }
}
.menu-btn--universal__burger {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}
.menu-btn--universal__text {
  font-size: 1rem;
  font-weight: var(--font-weight-bold);
  text-transform: uppercase;
  letter-spacing: 0.1em;
}
@media (max-width: 767px) {
  .menu-btn--universal__text {
    display: none;
  }
}

.menu-overlay {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  background: var(--white-bg, #ffffff);
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.3s ease;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  max-height: 60vh;
  overflow-y: auto;
}
.menu-overlay.active {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}
.menu-overlay__content {
  padding: 3rem 0;
}
@media (max-width: 1279px) {
  .menu-overlay--catalog {
    display: none;
  }
}
@media (min-width: 1280px) {
  .menu-overlay--universal {
    display: none;
  }
}

.catalog-menu h3 {
  font-size: 2.4rem;
  font-weight: var(--font-weight-bold);
  color: var(--black-text);
  margin-bottom: 2rem;
}
.catalog-menu__content {
  padding: 2rem 0;
  color: var(--text-color);
  font-size: 1.6rem;
}
.catalog-menu__content p {
  color: var(--grey-text);
  font-style: italic;
}

.universal-menu__section {
  margin-bottom: 4rem;
}
.universal-menu__section:last-child {
  margin-bottom: 0;
}
.universal-menu__section h3 {
  font-size: 2.4rem;
  font-weight: var(--font-weight-bold);
  color: var(--black-text);
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid var(--accent-color);
}
.universal-menu__list {
  display: flex;
  flex-direction: column;
  gap: 0;
}
.universal-menu__list li a {
  display: block;
  padding: 1.5rem 0;
  color: var(--black-text);
  text-decoration: none;
  font-size: 1.6rem;
  font-weight: var(--font-weight-medium);
  border-bottom: 1px solid var(--borders-color);
  transition: all 0.3s ease;
}
.universal-menu__list li a:hover {
  color: var(--accent-color);
  padding-left: 1rem;
}

.sections-menu h3 {
  font-size: 2.4rem;
  font-weight: var(--font-weight-bold);
  color: var(--black-text);
  margin-bottom: 2rem;
}
.sections-menu__list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}
.sections-menu__list li a {
  display: block;
  padding: 1.2rem 0;
  color: var(--black-text);
  text-decoration: none;
  font-size: 1.8rem;
  font-weight: var(--font-weight-medium);
  border-bottom: 1px solid var(--border-color, #e0e0e0);
  transition: all 0.3s ease;
}
.sections-menu__list li a:hover {
  color: var(--accent-color);
  padding-left: 1rem;
}

@media (max-width: 1400px) {
  .hide-on-1400 {
    display: none;
  }
}
.icons-wrapper {
  padding: 30px 0;
  display: flex;
  column-gap: 30px;
}

.icon {
  fill: transparent;
  stroke: transparent;
  width: 62px;
  height: 62px;
}

.icon--heart-line {
  fill: rgb(241, 68, 131);
}

.icon--id-card-line {
  fill: rgb(51, 51, 51);
}

.icon--search-line {
  fill: rgb(28, 176, 80);
}

.icon--user-star {
  fill: rgb(26, 134, 235);
}

.icon--user {
  stroke: rgb(26, 134, 235);
  transition: all 0.2s ease-in;
}
.icon--user:hover {
  stroke: rgb(17, 193, 90);
}

/* Nav Icon */
.mobile-nav-btn {
  --time: 0.1s;
  --width: 40px;
  --height: 30px;
  --line-height: 4px;
  --spacing: 6px;
  --color: #000;
  --radius: 4px;
  /* Fixed height and width */
  /* height: var(--height); */
  /* width: var(--width); */
  /* Dynamic height and width */
  height: calc(var(--line-height) * 3 + var(--spacing) * 2);
  width: var(--width);
  display: flex;
  justify-content: center;
  align-items: center;
}

.nav-icon {
  position: relative;
  width: var(--width);
  height: var(--line-height);
  background-color: var(--color);
  border-radius: var(--radius);
}

.nav-icon::before,
.nav-icon::after {
  content: "";
  display: block;
  position: absolute;
  left: 0;
  width: var(--width);
  height: var(--line-height);
  border-radius: var(--radius);
  background-color: var(--color);
  transition: transform var(--time) ease-in, top var(--time) linear var(--time);
}

.nav-icon::before {
  /* top: calc(var(--line-height) * -2); */
  top: calc(-1 * (var(--line-height) + var(--spacing)));
}

.nav-icon::after {
  /* top: calc(var(--line-height) * 2); */
  top: calc(var(--line-height) + var(--spacing));
}

.nav-icon.nav-icon--active {
  background-color: transparent;
}

.nav-icon.nav-icon--active::before,
.nav-icon.nav-icon--active::after {
  top: 0;
  transition: top var(--time) linear, transform var(--time) ease-in var(--time);
}

.nav-icon.nav-icon--active::before {
  transform: rotate(45deg);
}

.nav-icon.nav-icon--active::after {
  transform: rotate(-45deg);
}

/* Layout */
.mobile-nav-btn {
  z-index: 999;
}

.nav {
  font-size: 18px;
}

.nav__list {
  display: flex;
  column-gap: 30px;
}

.title-1 {
  margin: 1em 0 0.5em;
  font-size: 38px;
  font-weight: 700;
  font-family: var(--font-titles);
}

.title-2 {
  margin: 1em 0 0.5em;
  font-size: 32px;
  font-weight: 700;
  font-family: var(--font-titles);
}

.title-3 {
  margin: 1em 0 0.5em;
  font-size: 26px;
  font-weight: 700;
  font-family: var(--font-titles);
}

.title-4 {
  margin: 1em 0 0.5em;
  font-size: 18px;
  font-weight: 700;
  font-family: var(--font-titles);
}

/* No styles code below. Only in modules */
/* Не пишите CSS код ниже. Только в подключаемых файлах */
/*# sourceMappingURL=data:application/json;charset=utf8;base64,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 */
