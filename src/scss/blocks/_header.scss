.header {
}

.header__nav {
	@include tablet {
		display: none;
	}
}
.sub-header {
	display: flex;
	flex-direction: row;
	justify-content: space-between;
	text-transform: uppercase;
	font-weight: var(--font-weight-semibold);
	font-size: 1.3rem;
	color: var(--text-color);
	padding: 1rem 0;
	border-bottom: .1rem solid var(--borders-color);

	& a {
		color: var(--text-color);
		transition: color 0.3s ease;
	}

	& a:hover {
		color: var(--accent-color);

		.sub-header__icon-svg {
			color: var(--accent-color);
		}
	}
}

.sub-header__socials {
	display: flex;
	gap: 2rem;
}

.sub-header__calculate-phone {
	display: flex;
	gap: 2rem;
}

.sub-header__icon {
	display: inline-flex;
	align-items: center;
	gap: 0.8rem;
	color: var(--text-color);
	transition: color 0.3s ease;

	&:hover {
		color: var(--accent-color);

		.sub-header__icon-svg {
			color: var(--accent-color);
		}
	}
}

.sub-header__icon-svg {
	width: 2.4rem;
	height: 2.4rem;
	color: var(--text-color);
	transition: color 0.3s ease;
	flex-shrink: 0;
}

.phone {
	font-weight: var(--font-weight-bold);
}