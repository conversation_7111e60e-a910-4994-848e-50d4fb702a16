@charset "UTF-8";
/* Base */ /* Reset and base styles  */
* {
  padding: 0px;
  margin: 0px;
  border: none;
}

*,
*::before,
*::after {
  box-sizing: border-box;
}

/* Links */
a, a:link, a:visited {
  text-decoration: none;
}

a:hover {
  text-decoration: none;
}

/* Common */
aside, nav, footer, header, section, main {
  display: block;
}

h1, h2, h3, h4, h5, h6, p {
  font-size: inherit;
  font-weight: inherit;
}

ul, ul li {
  list-style: none;
}

img {
  vertical-align: top;
}

img, svg {
  max-width: 100%;
  height: auto;
}

address {
  font-style: normal;
}

/* Form */
input, textarea, button, select {
  font-family: inherit;
  font-size: inherit;
  color: inherit;
  background-color: transparent;
}

input::-ms-clear {
  display: none;
}

button, input[type=submit] {
  display: inline-block;
  box-shadow: none;
  background-color: transparent;
  background: none;
  cursor: pointer;
}

input:focus, input:active,
button:focus, button:active {
  outline: none;
}

button::-moz-focus-inner {
  padding: 0;
  border: 0;
}

label {
  cursor: pointer;
}

legend {
  display: block;
}

:root {
  --container-width: 1200px;
  --container-padding: 15px;
  --font-main: "Onest", sans-serif;
  --font-accent: "Manrope", sans-serif;
  --font-titles: var(--font-main);
  --font-legacy: "mainfont";
  --font-weight-thin: 100;
  --font-weight-extralight: 200;
  --font-weight-light: 300;
  --font-weight-regular: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;
  --font-weight-black: 900;
  --font-thin: var(--font-main);
  --font-extralight: var(--font-main);
  --font-light: var(--font-main);
  --font-regular: var(--font-main);
  --font-medium: var(--font-main);
  --font-semibold: var(--font-main);
  --font-bold: var(--font-main);
  --font-extrabold: var(--font-main);
  --font-black: var(--font-main);
  --page-bg: #fff;
  --text-color: #000;
  --accent: #ac182c;
  --link-color: #2578c8;
  --laptop-size: 1199px;
  --tablet-size: 959px;
  --mobile-size: 599px;
  --accent-color: #FF752B;
  --borders-color: rgba(178,188,195,.5);
}

.dark {
  --page-bg: #252526;
  --text-color: #fff;
}

@font-face {
  font-family: FirasansBook;
  font-display: swap;
  src: url("../fonts/FirasansBook.woff2") format("woff2"), url("../fonts/FirasansBook.woff") format("woff");
  font-weight: 400;
  font-style: normal;
}
@font-face {
  font-family: Montserrat;
  font-display: swap;
  src: url("../fonts/Montserrat-Bold.woff2") format("woff2"), url("../fonts/Montserrat-Bold.woff") format("woff");
  font-weight: 700;
  font-style: normal;
}
@font-face {
  font-family: Montserrat;
  font-display: swap;
  src: url("../fonts/Montserrat-BoldItalic.woff2") format("woff2"), url("../fonts/Montserrat-BoldItalic.woff") format("woff");
  font-weight: 400;
  font-style: normal;
}
@font-face {
  font-family: Montserrat;
  font-display: swap;
  src: url("../fonts/Montserrat-Regular.woff2") format("woff2"), url("../fonts/Montserrat-Regular.woff") format("woff");
  font-weight: 400;
  font-style: normal;
}
html {
  font-size: 62.5%;
  height: 100%;
  width: 100%;
  scroll-behavior: smooth;
}
@media (max-width: 1500px) {
  html {
    font-size: 62.5%;
  }
}
@media (max-width: 992px) {
  html {
    font-size: 60%;
  }
}
@media (max-width: 767px) {
  html {
    font-size: 58%;
  }
}
@media (max-width: 576px) {
  html {
    font-size: 56%;
  }
}

body {
  background-color: var(--bgr-color__dark-gray-original);
  color: var(--text__on-dark);
  font-family: var(--font-main);
  font-size: 1.6rem;
}
@media (max-width: 767px) {
  body {
    font-size: 1.4rem;
  }
}
@media (max-width: 576px) {
  body {
    font-size: 1.2rem;
  }
}

.btn {
  display: inline-flex;
}

.btn-normal .btn-click {
  -webkit-box-shadow: inset 0px 0px 0px 1px var(--borders__on-dark);
  box-shadow: inset 0px 0px 0px 1px var(--borders__on-dark);
}

.btn-normal .btn-click-white {
  -webkit-box-shadow: inset 0px 0px 0px 1px var(--headers__on-dark);
  box-shadow: inset 0px 0px 0px 1px var(--headers__on-dark);
}

.btn-click {
  cursor: pointer;
  border: 0;
  color: var(--text__on-dark);
  background: transparent;
  border-radius: 10rem !important;
  min-width: 1rem;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  text-decoration: none;
  will-change: transform;
  outline: 0;
  transform: translateZ(0) rotate(0.001deg);
  padding: 1.7rem 3rem !important;
  margin: 0 !important;
  text-decoration: none !important;
  font-size: 1.4rem;
}
@media (max-width: 992px) {
  .btn-click {
    font-size: 1.2rem;
    height: 4.8rem;
    padding: 1.7rem 2rem !important;
  }
}

.btn-click-small-padding {
  padding: 1rem 1.5rem !important;
}

.btn-click-white {
  cursor: pointer;
  border: 0;
  color: var(--headers__on-dark);
  background: transparent;
  border-radius: 10rem !important;
  min-width: 1rem;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  text-decoration: none;
  will-change: transform;
  outline: 0;
  transform: translateZ(0) rotate(0.001deg);
  padding: 1.7rem 3rem !important;
  margin: 0 !important;
  text-decoration: none !important;
  font-size: 1.4rem;
}
@media (max-width: 992px) {
  .btn-click-white {
    font-size: 1.2rem;
  }
}

.btn-bgr-dark {
  background: var(--bgr-color__dark-gray-original);
}

.btn-bgr-white {
  background: var(--headers__on-dark);
}

.btn-fill {
  background: var(--borders__on-dark);
  position: absolute;
  width: 150%;
  height: 200%;
  border-radius: 50%;
  top: -50%;
  left: -25%;
  transform: translate3d(0, -76%, 0);
  will-change: transform;
  transition: background-color ease-in-out 0.25s;
}

.btn-text {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  z-index: 2;
  color: var(--text__on-dark);
  position: relative;
  transform: rotate(0.001deg);
  pointer-events: none;
  will-change: transform, color;
}

.btn-normal .btn-text .btn-text-inner {
  color: var(--headers__on-dark) !important;
  display: flex;
  align-items: center;
  gap: 1rem;
  justify-content: center;
}

.pointernone {
  pointer-events: none;
}

.zindex {
  z-index: 9999999999999;
  pointer-events: all;
}

.btn-text-inner img {
  width: 2.4rem;
}

@media (max-width: 992) {
  .btn-text-inner img {
    width: 1.4rem;
  }
}
@media (min-width: 993) {
  .btn-text-inner img {
    width: 1.5rem;
  }
}
@media (max-width: 1400) {
  .btn-text-inner img {
    width: 1.6rem;
  }
}
.uppercase {
  text-transform: uppercase;
}

.main-button {
  font-size: 1.4rem;
  color: var(--headers__on-dark);
  background: var(--bgr-color__dark-gray-original);
  padding: 0.3rem 0.3rem 0.3rem 2.5rem;
  border: 1px solid var(--headers__on-dark);
  border-radius: 50rem;
  align-items: center;
  height: 5.8rem;
  display: flex;
  justify-content: flex-start;
  position: relative;
  z-index: 1;
}
@media (max-width: 992px) {
  .main-button {
    font-size: 1.2rem;
    padding: 0.3rem 0.3rem 0.3rem 0.9rem;
    height: 4.6rem;
  }
}
.main-button__icon {
  border-radius: 10rem;
  background: linear-gradient(180deg, #FFF 0%, #BABABA 100%);
  box-shadow: -1px 0px 10.3px 0px rgba(0, 0, 0, 0.11);
  width: 5.2rem;
  height: 5.2rem;
  display: flex;
  justify-content: end;
  align-items: center;
  margin-left: 1.5rem;
  position: absolute;
  right: 0.2rem;
  transition: var(--animation-smooth);
  z-index: -1;
}
.main-button__icon img {
  height: 50%;
  padding-right: 1.3rem;
}
@media (max-width: 992px) {
  .main-button__icon img {
    padding-right: 1rem;
  }
}
@media (max-width: 992px) {
  .main-button__icon {
    width: 4rem;
    height: 4rem;
    margin-left: 1rem;
  }
}
.main-button:hover {
  color: var(--borders__on-dark);
}
.main-button:hover .main-button__icon {
  width: calc(100% - 0.4rem);
}
.main-button__dummy {
  visibility: hidden;
  width: 5.2rem;
  height: 5.2rem;
  margin-left: 1.5rem;
}
@media (max-width: 992px) {
  .main-button__dummy {
    width: 4rem;
    height: 4rem;
    margin-left: 1rem;
  }
}

::-moz-selection {
  background-color: var(--borders__on-dark);
  color: var(--headers__on-dark);
}

::selection {
  background-color: var(--borders__on-dark);
  color: var(--headers__on-dark);
}

.docs {
  display: grid;
  line-height: 1.5;
}
.docs p {
  margin: 1rem 0;
}
.docs ul,
.docs ol {
  padding-left: 2rem;
}
.docs ul li,
.docs ol li {
  list-style: disc;
  margin-bottom: 0.5rem;
}
.docs ol li {
  list-style: decimal;
}
.docs section, .docs section.docs {
  padding: 40px 0;
}
.docs section + section {
  border-top: 1px solid #dae5e9;
}
.docs small {
  font-size: 1rem;
  color: rgb(172, 172, 172);
}
.docs .title-1:first-child,
.docs .title-2:first-child {
  margin-top: 0 !important;
}

.test {
  width: 600px;
  height: 300px;
  margin: 50px auto;
  background-color: #999;
  background-position: center center; /* x y */
  background-size: cover;
  background-repeat: no-repeat;
  background-image: url("./../img/project-02.jpg");
}
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .test {
    background-image: url("./../img/<EMAIL>");
  }
}

.test-2 {
  width: 600px;
  height: 300px;
  margin: 50px auto;
  background-color: #999;
  background-position: center center; /* x y */
  background-size: cover;
  background-repeat: no-repeat;
  background-image: image-set(url("./../img/project-02.jpg") 1x, url("./../img/<EMAIL>") 2x);
}

.font-1 {
  font-family: "Montserrat";
  font-weight: 700;
  font-style: italic;
}

.font-2 {
  font-family: "FirasansBook";
  font-weight: 400;
}

/* Отключить при необходимости */
.none {
  display: none !important;
}

.visually-hidden {
  position: absolute;
  width: 1px;
  height: 1px;
  margin: -1px;
  border: 0;
  padding: 0;
  white-space: nowrap;
  clip-path: inset(100%);
  clip: rect(0 0 0 0);
  overflow: hidden;
}

.no-scroll {
  overflow-y: hidden;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.text-center {
  text-align: center;
}

.d-flex {
  display: flex;
}

.flex-center {
  justify-content: center;
}

.container {
  margin: 0 auto;
  border-left: 1px solid var(--borders__on-dark);
  border-right: 1px solid var(--borders__on-dark);
}
@media (min-width: 1700px) {
  .container {
    margin: auto 8rem;
    padding: 0 5rem;
  }
}
@media (min-width: 993px) and (max-width: 1699px) {
  .container {
    margin: auto 5rem;
    padding: 0 3rem;
  }
}
@media (max-width: 992px) {
  .container {
    margin: auto 3rem;
    padding: 0 2rem;
  }
}
@media (max-width: 767px) {
  .container {
    margin: auto 2rem;
    padding: 0 2rem;
  }
}
@media (max-width: 576px) {
  .container {
    margin: auto 1.6rem;
    padding: 0 1.6rem;
  }
}

.container-grid-section {
  margin: 0 auto;
  height: 100%;
  min-height: inherit;
  border-left: 1px solid var(--borders__on-dark);
  border-right: 1px solid var(--borders__on-dark);
  position: relative;
  z-index: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
@media (min-width: 1700px) {
  .container-grid-section {
    margin: auto 8rem;
  }
}
@media (min-width: 993px) and (max-width: 1699px) {
  .container-grid-section {
    margin: auto 5rem;
  }
}
@media (max-width: 992px) {
  .container-grid-section {
    margin: auto 3rem;
  }
}
@media (max-width: 767px) {
  .container-grid-section {
    margin: auto 2rem;
  }
}
@media (max-width: 576px) {
  .container-grid-section {
    margin: auto 1.6rem;
  }
}

.container-no-border {
  margin: 0 auto;
}
@media (min-width: 1700px) {
  .container-no-border {
    margin: auto 8rem;
    padding: 0 5rem;
  }
}
@media (min-width: 993px) and (max-width: 1699px) {
  .container-no-border {
    margin: auto 5rem;
    padding: 0 3rem;
  }
}
@media (max-width: 992px) {
  .container-no-border {
    margin: auto 3rem;
    padding: 0 2rem;
  }
}
@media (max-width: 767px) {
  .container-no-border {
    margin: auto 2rem;
    padding: 0 2rem;
  }
}
@media (max-width: 576px) {
  .container-no-border {
    margin: auto 1.6rem;
    padding: 0 1.6rem;
  }
}

.container-inside-container {
  position: relative;
}
@media (min-width: 1700px) {
  .container-inside-container {
    padding: 0 5rem;
  }
}
@media (min-width: 993px) and (max-width: 1699px) {
  .container-inside-container {
    padding: 0 3rem;
  }
}
@media (max-width: 992px) {
  .container-inside-container {
    padding: 0 2rem;
  }
}
@media (max-width: 767px) {
  .container-inside-container {
    padding: 0 2rem;
  }
}
@media (max-width: 576px) {
  .container-inside-container {
    padding: 0 1.6rem;
  }
}

.lines {
  pointer-events: none;
  position: absolute;
  z-index: -5;
  background: transparent;
  width: 100%;
  height: 100%;
  opacity: 1;
  display: flex;
  flex-grow: 0;
  flex-shrink: 0;
  flex-wrap: nowrap;
}
.lines__empty {
  width: calc(25% - 1px);
  background: transparent;
  height: 100%;
  opacity: 0.1;
}
@media (max-width: 992px) {
  .lines__empty {
    width: calc(33.33333% - 1px);
  }
}
.lines__line {
  width: 1px;
  background-color: var(--borders__on-dark);
  height: 100%;
}
.lines__line--invisible {
  width: 1px;
  background-color: transparent;
  height: 100%;
}

.lines-md-2 {
  pointer-events: none;
  position: absolute;
  z-index: -5;
  background: transparent;
  width: 100%;
  height: 100%;
  opacity: 1;
  display: flex;
  flex-grow: 0;
  flex-shrink: 0;
  flex-wrap: nowrap;
}
.lines-md-2__empty {
  width: calc(25% - 1px);
  background: transparent;
  height: 100%;
  opacity: 0.1;
}
@media (max-width: 992px) {
  .lines-md-2__empty {
    width: calc(50% - 1px);
  }
}
.lines-md-2__line {
  width: 1px;
  background-color: var(--borders__on-dark);
  height: 100%;
}

.main-menu-open .container {
  border-left: 1px solid rgba(255, 0, 0, 0);
  border-right: 1px solid rgba(255, 0, 0, 0);
}

.disable-padding {
  padding: 0 !important;
}

.borders-l-r {
  border-left: 1px solid var(--borders__on-dark);
  border-right: 1px solid var(--borders__on-dark);
}

.borders-t {
  border-top: 1px solid var(--borders__on-dark);
}

.borders-b {
  border-bottom: 1px solid var(--borders__on-dark);
}

.borders-t-b {
  border-top: 1px solid var(--borders__on-dark);
  border-bottom: 1px solid var(--borders__on-dark);
}

.borders-all {
  border: 1px solid var(--borders__on-dark);
}

.disable-borders {
  border: none !important;
}

.flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.flex_fw {
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.flex_aic {
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.flex_aife {
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: flex-end;
}

.flex_jcc {
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.flex_jcsb {
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
}

@media (max-width: 767px) {
  .flex_mobile-column {
    flex-direction: column;
  }
}
.container-right {
  /* overflow-x: hidden; */
  padding-left: calc((100% - var(--container-width)) / 2 + var(--container-padding));
}
@media (max-width: var(--laptop-size)) {
  .container-right {
    padding-left: var(--container-padding);
  }
}

.grid-section__content {
  font-size: 2.5rem;
  line-height: 140%;
}

.container-left {
  /* overflow-x: hidden; */
  padding-right: calc((100% - var(--container-width)) / 2 + var(--container-padding));
}
@media (max-width: var(--laptop-size)) {
  .container-left {
    padding-right: var(--container-padding);
  }
}

.mt-auto {
  margin-top: auto;
}

.mt-50 {
  margin-top: 5rem;
}

.pt-50 {
  padding-top: 5rem;
}
@media (max-width: 992px) {
  .pt-50 {
    padding-bottom: 4rem;
  }
}
@media (max-width: 767px) {
  .pt-50 {
    padding-bottom: 2rem;
  }
}
@media (max-width: 567px) {
  .pt-50 {
    padding-bottom: 1.6rem;
  }
}

.pb-50 {
  padding-bottom: 5rem;
}
@media (max-width: 992px) {
  .pb-50 {
    padding-bottom: 4rem;
  }
}
@media (max-width: 767px) {
  .pb-50 {
    padding-bottom: 2rem;
  }
}
@media (max-width: 567px) {
  .pb-50 {
    padding-bottom: 1.6rem;
  }
}

.pb-30 {
  padding-bottom: 3rem;
}
@media (max-width: 992px) {
  .pb-30 {
    padding-bottom: 3rem;
  }
}
@media (max-width: 767px) {
  .pb-30 {
    padding-bottom: 2rem;
  }
}
@media (max-width: 567px) {
  .pb-30 {
    padding-bottom: 1.6rem;
  }
}

.gap-3 {
  gap: 3rem;
}

.mb-3 {
  margin-bottom: 3rem;
}
@media (max-width: 992px) {
  .mb-3 {
    margin-bottom: 2rem;
  }
}
@media (max-width: 567px) {
  .mb-3 {
    margin-bottom: 1.6rem;
  }
}

.spares-block ul {
  margin-bottom: 1rem;
}

.spares-block ul li {
  list-style: disc;
  list-style-position: inside;
  padding-bottom: 1rem;
  line-height: 150%;
}

.mw770 {
  max-width: 77rem;
}
@media (max-width: 992px) {
  .mw770 {
    max-width: 100%;
  }
}
.m-0-auto {
  margin: 0 auto;
}

html, body {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.footer {
  margin-top: auto;
}

.footer {
  padding: 60px 0;
  background-color: #e3e3e3;
}

/* Blocks */
.footer {
  background-color: rgb(39, 39, 39);
  padding: 50px 0;
  font-size: 32px;
  color: #fff;
}
.footer h1 {
  font-size: 32px;
}
.footer a {
  color: #fff;
  text-decoration: underline;
}
@media (max-width: 1200px) {
  .footer {
    font-size: 26px;
  }
}

.footer__copyright {
  padding: 10px 0;
  font-size: 16px;
}

@media (max-width: 1220px) {
  .header__nav {
    display: none;
  }
}

.sub-header {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  text-transform: uppercase;
  font-weight: var(--font-weight-semibold);
  font-size: 1.3rem;
  color: var(--text-color);
  padding: 1rem 0;
  border-bottom: 0.1rem solid var(--borders-color);
}
.sub-header a {
  color: var(--text-color);
}
.sub-header a:hover {
  color: var(--accent-color);
}

.sub-header__icon {
  background-image: url("./../img/svgicons/whatsapp.svg");
  background-size: 2rem;
  background-position: left center;
  background-repeat: no-repeat;
  padding-left: 3rem;
}

.phone {
  font-weight: var(--font-weight-bold);
}

.icons-wrapper {
  padding: 30px 0;
  display: flex;
  column-gap: 30px;
}

.icon {
  fill: transparent;
  stroke: transparent;
  width: 62px;
  height: 62px;
}

.icon--heart-line {
  fill: rgb(241, 68, 131);
}

.icon--id-card-line {
  fill: rgb(51, 51, 51);
}

.icon--search-line {
  fill: rgb(28, 176, 80);
}

.icon--user-star {
  fill: rgb(26, 134, 235);
}

.icon--user {
  stroke: rgb(26, 134, 235);
  transition: all 0.2s ease-in;
}
.icon--user:hover {
  stroke: rgb(17, 193, 90);
}

.logo {
  font-size: 32px;
}

.mobile-nav {
  position: fixed;
  top: -100%;
  width: 100%;
  height: 100%;
  z-index: 99;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: 40px;
  padding-bottom: 40px;
  background: #8ccae6;
  transition: all 0.2s ease-in;
}

.mobile-nav--open {
  top: 0;
}

.mobile-nav a {
  color: #fff;
}

.mobile-nav__list {
  display: flex;
  flex-direction: column;
  align-items: center;
  row-gap: 20px;
  font-size: 28px;
}
.mobile-nav__list .active {
  opacity: 0.5;
}

/* Nav Icon */
.mobile-nav-btn {
  --time: 0.1s;
  --width: 40px;
  --height: 30px;
  --line-height: 4px;
  --spacing: 6px;
  --color: #000;
  --radius: 4px;
  /* Fixed height and width */
  /* height: var(--height); */
  /* width: var(--width); */
  /* Dynamic height and width */
  height: calc(var(--line-height) * 3 + var(--spacing) * 2);
  width: var(--width);
  display: flex;
  justify-content: center;
  align-items: center;
}

.nav-icon {
  position: relative;
  width: var(--width);
  height: var(--line-height);
  background-color: var(--color);
  border-radius: var(--radius);
}

.nav-icon::before,
.nav-icon::after {
  content: "";
  display: block;
  position: absolute;
  left: 0;
  width: var(--width);
  height: var(--line-height);
  border-radius: var(--radius);
  background-color: var(--color);
  transition: transform var(--time) ease-in, top var(--time) linear var(--time);
}

.nav-icon::before {
  /* top: calc(var(--line-height) * -2); */
  top: calc(-1 * (var(--line-height) + var(--spacing)));
}

.nav-icon::after {
  /* top: calc(var(--line-height) * 2); */
  top: calc(var(--line-height) + var(--spacing));
}

.nav-icon.nav-icon--active {
  background-color: transparent;
}

.nav-icon.nav-icon--active::before,
.nav-icon.nav-icon--active::after {
  top: 0;
  transition: top var(--time) linear, transform var(--time) ease-in var(--time);
}

.nav-icon.nav-icon--active::before {
  transform: rotate(45deg);
}

.nav-icon.nav-icon--active::after {
  transform: rotate(-45deg);
}

/* Layout */
.mobile-nav-btn {
  z-index: 999;
}

.nav {
  font-size: 18px;
}

.nav__list {
  display: flex;
  column-gap: 30px;
}

.title-1 {
  margin: 1em 0 0.5em;
  font-size: 38px;
  font-weight: 700;
  font-family: var(--font-titles);
}

.title-2 {
  margin: 1em 0 0.5em;
  font-size: 32px;
  font-weight: 700;
  font-family: var(--font-titles);
}

.title-3 {
  margin: 1em 0 0.5em;
  font-size: 26px;
  font-weight: 700;
  font-family: var(--font-titles);
}

.title-4 {
  margin: 1em 0 0.5em;
  font-size: 18px;
  font-weight: 700;
  font-family: var(--font-titles);
}

/* No styles code below. Only in modules */
/* Не пишите CSS код ниже. Только в подключаемых файлах */
/*# sourceMappingURL=data:application/json;charset=utf8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIm1haW4uc2NzcyIsImJhc2UvX3Jlc2V0LnNjc3MiLCJiYXNlL192YXJzLnNjc3MiLCJiYXNlL19mb250c0F1dG9HZW4uc2NzcyIsImJhc2UvX2Jhc2Uuc2NzcyIsImJhc2UvX2RvY3Muc2NzcyIsImJhc2UvX21peGlucy5zY3NzIiwiYmFzZS9fdXRpbHMuc2NzcyIsImJhc2UvX2NvbnRhaW5lcnMuc2NzcyIsImJhc2UvX3N0aWNreS1mb290ZXIuc2NzcyIsImJsb2Nrcy9fZm9vdGVyLnNjc3MiLCJibG9ja3MvX2hlYWRlci5zY3NzIiwiYmxvY2tzL19pY29ucy5zY3NzIiwiYmxvY2tzL19sb2dvLnNjc3MiLCJibG9ja3MvX21vYmlsZS1uYXYuc2NzcyIsImJsb2Nrcy9fbmF2LWljb24uc2NzcyIsImJsb2Nrcy9fbmF2LnNjc3MiLCJibG9ja3MvX3RpdGxlcy5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7QUFBQTtBQ0NBO0VBQ0M7RUFDQTtFQUNBOzs7QUFHRDtBQUFBO0FBQUE7RUFHQzs7O0FBR0Q7QUFFQTtFQUNJOzs7QUFHSjtFQUNJOzs7QUFHSjtBQUVBO0VBQ0M7OztBQUdEO0VBQ0k7RUFDSDs7O0FBR0Q7RUFDQzs7O0FBR0Q7RUFDQzs7O0FBR0Q7RUFDQztFQUNBOzs7QUFHRDtFQUNFOzs7QUFHRjtBQUVBO0VBQ0M7RUFDRztFQUNBO0VBQ0E7OztBQUdKO0VBQ0M7OztBQUdEO0VBQ0k7RUFDQTtFQUNBO0VBQ0E7RUFDQTs7O0FBR0o7QUFBQTtFQUVJOzs7QUFHSjtFQUNDO0VBQ0E7OztBQUdEO0VBQ0M7OztBQUdEO0VBQ0M7OztBQ3ZGRDtFQUNDO0VBQ0E7RUFHQTtFQUNBO0VBQ0E7RUFDQTtFQUdBO0VBQ0E7RUFDQTtFQUNBO0VBQ0E7RUFDQTtFQUNBO0VBQ0E7RUFDQTtFQUdBO0VBQ0E7RUFDQTtFQUNBO0VBQ0E7RUFDQTtFQUNBO0VBQ0E7RUFDQTtFQUVBO0VBQ0E7RUFDQTtFQUNBO0VBR0c7RUFDSDtFQUNBO0VBR0E7RUFHQTs7O0FBR0Q7RUFDSTtFQUNBOzs7QUNuREo7RUFDQztFQUNBO0VBQ0E7RUFDQTtFQUNBOztBQUVEO0VBQ0M7RUFDQTtFQUNBO0VBQ0E7RUFDQTs7QUFFRDtFQUNDO0VBQ0E7RUFDQTtFQUNBO0VBQ0E7O0FBRUQ7RUFDQztFQUNBO0VBQ0E7RUFDQTtFQUNBOztBQzFCRDtFQUNJO0VBQ0E7RUFDQTtFQUNIOztBQUdBO0VBUEQ7SUFRRTs7O0FBRUQ7RUFWRDtJQVdFOzs7QUFFRDtFQWJEO0lBY0U7OztBQUVEO0VBaEJEO0lBaUJFOzs7O0FBSUY7RUFDQztFQUNBO0VBQ0c7RUFDQTs7QUFDQTtFQUxKO0lBTVE7OztBQUVKO0VBUko7SUFTUTs7OztBQUlSO0VBQ0k7OztBQUVKO0VBQ0k7RUFDQTs7O0FBRUo7RUFDSTtFQUNBOzs7QUFFSjtFQUNJO0VBQ0E7RUFDQTtFQUNBO0VBQ0E7RUFDQTtFQUNBO0VBQ0E7RUFDQTtFQUNBO0VBQ0E7RUFDQTtFQUNBO0VBQ0E7RUFDQTtFQUNIO0VBQ0E7RUFDQTtFQUNHOztBQUVBO0VBckJKO0lBc0JFO0lBQ007SUFDQTs7OztBQUdSO0VBQ0M7OztBQUdEO0VBQ0k7RUFDQTtFQUNBO0VBQ0E7RUFDQTtFQUNBO0VBQ0E7RUFDQTtFQUNBO0VBQ0E7RUFDQTtFQUNBO0VBQ0E7RUFDQTtFQUNBO0VBQ0g7RUFDQTtFQUNBO0VBQ0c7O0FBRUE7RUFyQko7SUFzQkU7Ozs7QUFLRjtFQUNJOzs7QUFFSjtFQUNJOzs7QUFFSjtFQUNJO0VBQ0E7RUFDQTtFQUNBO0VBQ0E7RUFDQTtFQUNBO0VBQ0E7RUFDQTtFQUNBOzs7QUFFSjtFQUNJO0VBQ0E7RUFDQTtFQUNBO0VBQ0E7RUFDQTtFQUNBO0VBQ0E7RUFDQTtFQUNBO0VBQ0E7OztBQUVKO0VBQ0k7RUFDSDtFQUNHO0VBQ0E7RUFDQTs7O0FBRUo7RUFDSTs7O0FBRUo7RUFDSTtFQUNBOzs7QUFJSDtFQUNDOzs7QUFHRjtFQUNtQjtJQUFNOzs7QUFFekI7RUFDbUI7SUFBTTs7O0FBRXpCO0VBQ21CO0lBQU07OztBQUV6QjtFQUNDOzs7QUFJRDtFQUNJO0VBQ0E7RUFDQTtFQUNBO0VBQ0E7RUFDQTtFQUNBO0VBQ0E7RUFDQTtFQUNBO0VBQ0E7RUFDQTs7QUFFQTtFQWRKO0lBZUU7SUFDTTtJQUNBOzs7QUFJSjtFQUNJO0VBQ0E7RUFDQTtFQUNBO0VBQ0E7RUFDQTtFQUNBO0VBQ0E7RUFDQTtFQUNBO0VBQ0E7RUFDQTtFQUNBOztBQUVBO0VBQ0k7RUFDQTs7QUFFQTtFQUpKO0lBS1E7OztBQUlSO0VBeEJKO0lBeUJRO0lBQ0E7SUFDQTs7O0FBR1I7RUFDSTs7QUFFSjtFQUNJOztBQUdKO0VBQ0k7RUFDQTtFQUNBO0VBQ0E7O0FBRUE7RUFOSjtJQU9RO0lBQ0E7SUFDQTs7OztBQUtaO0VBQ0k7RUFDQTs7O0FBR0o7RUFDSTtFQUNBOzs7QUN2UEo7RUFDQztFQUVBOztBQUVBO0VBQ0M7O0FBR0Q7QUFBQTtFQUVDOztBQUVBO0FBQUE7RUFDQztFQUNBOztBQUlGO0VBQ0M7O0FBR0Q7RUFDQzs7QUFHRDtFQUNDOztBQUdEO0VBQ0M7RUFDQTs7QUFHRDtBQUFBO0VBRUM7OztBQUlGO0VBQ0M7RUFDQTtFQUNBO0VBQ0E7RUFDQTtFQUNBO0VBQ0E7RUFJQTs7QUNwREE7RUR5Q0Q7SUFhRTs7OztBQVNGO0VBQ0M7RUFDQTtFQUNBO0VBQ0E7RUFDQTtFQUNBO0VBQ0E7RUFHQTs7O0FBS0Q7RUFDQztFQUNBO0VBQ0E7OztBQUdEO0VBQ0M7RUFDQTs7O0FMakZzQjtBT052QjtFQUNDOzs7QUFHRDtFQUNDO0VBQ0E7RUFDQTtFQUNBO0VBQ0E7RUFDQTtFQUNBO0VBQ0E7RUFDQTtFQUNBOzs7QUFHRDtFQUNDOzs7QUFHRDtFQUNDOzs7QUFHRDtFQUNDOzs7QUFHRDtFQUNDOzs7QUFHRDtFQUNDOzs7QUFHRDtFQUNDOzs7QUN0Q0Q7RUFDQztFQUNBO0VBQ0E7O0FBRUE7RUFMRDtJQU1FO0lBQ0E7OztBQUVEO0VBVEQ7SUFVRTtJQUNBOzs7QUFFRDtFQWJEO0lBY0U7SUFDQTs7O0FBRUQ7RUFqQkQ7SUFrQkU7SUFDQTs7O0FBRUQ7RUFyQkQ7SUFzQkU7SUFDQTs7OztBQUlGO0VBQ0M7RUFDQTtFQUNBO0VBQ0E7RUFDQTtFQUNBO0VBQ0E7RUFDQTtFQUVBO0VBQ0c7RUFDQTs7QUFFSDtFQWREO0lBZUU7OztBQUVEO0VBakJEO0lBa0JFOzs7QUFFRDtFQXBCRDtJQXFCRTs7O0FBRUQ7RUF2QkQ7SUF3QkU7OztBQUVEO0VBMUJEO0lBMkJFOzs7O0FBSUY7RUFDQzs7QUFFQTtFQUhEO0lBSUU7SUFDQTs7O0FBRUQ7RUFQRDtJQVFFO0lBQ0E7OztBQUVEO0VBWEQ7SUFZRTtJQUNBOzs7QUFFRDtFQWZEO0lBZ0JFO0lBQ0E7OztBQUVEO0VBbkJEO0lBb0JFO0lBQ0E7Ozs7QUFJRjtFQUNDOztBQUVBO0VBSEQ7SUFJRTs7O0FBRUQ7RUFORDtJQU9FOzs7QUFFRDtFQVREO0lBVUU7OztBQUVEO0VBWkQ7SUFhRTs7O0FBRUQ7RUFmRDtJQWdCRTs7OztBQUlGO0VBQ0M7RUFDQTtFQUNBO0VBQ0c7RUFDQTtFQUNBO0VBQ0E7RUFDQTtFQUNBO0VBQ0E7RUFDQTs7QUFFQTtFQUNJO0VBQ0E7RUFDQTtFQUNBOztBQUVBO0VBTko7SUFPUTs7O0FBR1I7RUFDSTtFQUNBO0VBQ0E7O0FBR1A7RUFDQztFQUNNO0VBQ0E7OztBQUtSO0VBQ0M7RUFDQTtFQUNBO0VBQ0c7RUFDQTtFQUNBO0VBQ0E7RUFDQTtFQUNBO0VBQ0E7RUFDQTs7QUFFQTtFQUNJO0VBQ0E7RUFDQTtFQUNBOztBQUVBO0VBTko7SUFPUTs7O0FBR1I7RUFDSTtFQUNBO0VBQ0E7OztBQUlSO0VBQ0M7RUFDQTs7O0FBS0Q7RUFDQzs7O0FBRUQ7RUFDQztFQUNBOzs7QUFFRDtFQUNDOzs7QUFFRDtFQUNDOzs7QUFFRDtFQUNDO0VBQ0E7OztBQUVEO0VBQ0M7OztBQUVEO0VBQ0M7OztBQUtEO0VBQ0k7RUFDQTtFQUNBOzs7QUFHSjtFQUNJO0VBQ0E7OztBQUdKO0VBQ0k7RUFDQTtFQUNBOzs7QUFHSjtFQUNJO0VBQ0E7RUFDQTs7O0FBR0o7RUFDSTtFQUNBO0VBQ0E7OztBQUdKO0VBQ0k7RUFDQTtFQUNBOzs7QUFFSjtFQUNBO0lBQ0M7OztBQU1EO0FBQ0M7RUFDQTs7QUFJQTtFQU5EO0lBT0U7Ozs7QUFJRjtFQUNDO0VBQ0E7OztBQUdEO0FBQ0M7RUFDQTs7QUFJQTtFQU5EO0lBT0U7Ozs7QUFJRjtFQUNJOzs7QUFHSjtFQUNDOzs7QUFFRDtFQUNDOztBQUNBO0VBRkQ7SUFHRTs7O0FBRUQ7RUFMRDtJQU1FOzs7QUFFRDtFQVJEO0lBU0U7Ozs7QUFHRjtFQUNDOztBQUNBO0VBRkQ7SUFHRTs7O0FBRUQ7RUFMRDtJQU1FOzs7QUFFRDtFQVJEO0lBU0U7Ozs7QUFHRjtFQUNDOztBQUNBO0VBRkQ7SUFHRTs7O0FBRUQ7RUFMRDtJQU1FOzs7QUFFRDtFQVJEO0lBU0U7Ozs7QUFHRjtFQUNDOzs7QUFFRDtFQUNDOztBQUVBO0VBSEQ7SUFJRTs7O0FBS0Q7RUFURDtJQVVFOzs7O0FBSUY7RUFDQzs7O0FBRUQ7RUFDQztFQUNHO0VBQ0E7RUFDQTs7O0FBSUo7RUFDQzs7QUFFQTtFQUhEO0lBRzJCOzs7QUFHM0I7RUFDQzs7O0FDOVZEO0VBQ0k7RUFDQTtFQUNBOzs7QUFHSjtFQUNJOzs7QUFHSjtFQUNDO0VBQ0E7OztBVEREO0FVWEE7RUFDQztFQUNBO0VBQ0E7RUFDRzs7QUFFSDtFQUNDOztBQUdEO0VBRUM7RUFDQTs7QUFHRDtFQWhCRDtJQWlCRTs7OztBQUlGO0VBQ0M7RUFDQTs7O0FKaEJHO0VLSko7SUFFRTs7OztBQUdGO0VBQ0M7RUFDQTtFQUNBO0VBQ0E7RUFDQTtFQUNBO0VBQ0E7RUFDQTtFQUNBOztBQUVBO0VBQ0E7O0FBRUE7RUFDQzs7O0FBTUY7RUFDQztFQUNBO0VBQ0E7RUFDQTtFQUNBOzs7QUFFRDtFQUNDOzs7QUNyQ0Q7RUFDQztFQUNBO0VBQ0E7OztBQUdEO0VBQ0M7RUFDQTtFQUNBO0VBQ0E7OztBQUdEO0VBQ0M7OztBQUdEO0VBQ0M7OztBQUVEO0VBQ0M7OztBQUdEO0VBQ0M7OztBQUdEO0VBQ0M7RUFDQTs7QUFFQTtFQUNDOzs7QUNqQ0Y7RUFDSTs7O0FDREo7RUFDQztFQUVBO0VBQ0E7RUFDQTtFQUNHO0VBRUg7RUFDRztFQUNBO0VBQ0g7RUFDRztFQUNBO0VBRUg7RUFDQTs7O0FBR0Q7RUFDQzs7O0FBR0Q7RUFDQzs7O0FBR0Q7RUFDQztFQUNBO0VBQ0E7RUFDQTtFQUNBOztBQUVBO0VBQ0M7OztBQ25DRjtBQUNBO0VBQ0M7RUFFQTtFQUNBO0VBRUE7RUFDQTtFQUVBO0VBQ0E7QUFFQTtBQUNBO0FBQ0E7QUFFQTtFQUNBO0VBQ0E7RUFFQTtFQUNBO0VBQ0E7OztBQUdEO0VBQ0M7RUFDQTtFQUNBO0VBQ0E7RUFDQTs7O0FBR0Q7QUFBQTtFQUVDO0VBQ0E7RUFFQTtFQUNBO0VBRUE7RUFDQTtFQUVBO0VBQ0E7RUFDQTs7O0FBSUQ7QUFDQztFQUNBOzs7QUFHRDtBQUNDO0VBQ0E7OztBQUdEO0VBQ0M7OztBQUdEO0FBQUE7RUFFQztFQUNBOzs7QUFJRDtFQUNDOzs7QUFHRDtFQUNDOzs7QUFHRDtBQUVBO0VBQ0M7OztBQ2pGRDtFQUNDOzs7QUFFRDtFQUNDO0VBQ0E7OztBQ1BEO0VBQ0M7RUFDQTtFQUNBO0VBQ0E7OztBQUdEO0VBQ0M7RUFDQTtFQUNBO0VBQ0E7OztBQUdEO0VBQ0M7RUFDQTtFQUNBO0VBQ0E7OztBQUdEO0VBQ0M7RUFDQTtFQUNBO0VBQ0E7OztBakJKRDtBQUNBIiwiZmlsZSI6Im1haW4uY3NzIiwic291cmNlc0NvbnRlbnQiOlsiLyogQmFzZSAqL1xuQGltcG9ydCAnLi9iYXNlL3Jlc2V0JztcbkBpbXBvcnQgJy4vYmFzZS92YXJzJztcbkBpbXBvcnQgJy4vYmFzZS9taXhpbnMnO1xuQGltcG9ydCAnLi9iYXNlL2ZvbnRzQXV0b0dlbic7XG5AaW1wb3J0ICcuL2Jhc2UvYmFzZSc7XG5AaW1wb3J0ICcuL2Jhc2UvZG9jcyc7IC8qINCe0YLQutC70Y7Rh9C40YLRjCDQv9GA0Lgg0L3QtdC+0LHRhdC+0LTQuNC80L7RgdGC0LggKi9cbkBpbXBvcnQgJy4vYmFzZS91dGlscyc7IC8vINCi0LXRgdGCINC90LAg0L7RiNC40LHQutGDXG5AaW1wb3J0ICcuL2Jhc2UvY29udGFpbmVycyc7XG5AaW1wb3J0ICcuL2Jhc2Uvc3RpY2t5LWZvb3Rlcic7XG5cbi8qIEJsb2NrcyAqL1xuQGltcG9ydCBcImJsb2Nrcy9fZm9vdGVyLnNjc3NcIjtcbkBpbXBvcnQgXCJibG9ja3MvX2hlYWRlci5zY3NzXCI7XG5AaW1wb3J0IFwiYmxvY2tzL19pY29ucy5zY3NzXCI7XG5AaW1wb3J0IFwiYmxvY2tzL19sb2dvLnNjc3NcIjtcbkBpbXBvcnQgXCJibG9ja3MvX21vYmlsZS1uYXYuc2Nzc1wiO1xuQGltcG9ydCBcImJsb2Nrcy9fbmF2LWljb24uc2Nzc1wiO1xuQGltcG9ydCBcImJsb2Nrcy9fbmF2LnNjc3NcIjtcbkBpbXBvcnQgXCJibG9ja3MvX3RpdGxlcy5zY3NzXCI7XG5cbi8qIE5vIHN0eWxlcyBjb2RlIGJlbG93LiBPbmx5IGluIG1vZHVsZXMgKi9cbi8qINCd0LUg0L/QuNGI0LjRgtC1IENTUyDQutC+0LQg0L3QuNC20LUuINCi0L7Qu9GM0LrQviDQsiDQv9C+0LTQutC70Y7Rh9Cw0LXQvNGL0YUg0YTQsNC50LvQsNGFICovXG4iLCIvKiBSZXNldCBhbmQgYmFzZSBzdHlsZXMgICovXG4qIHtcblx0cGFkZGluZzogMHB4O1xuXHRtYXJnaW46IDBweDtcblx0Ym9yZGVyOiBub25lO1xufVxuXG4qLFxuKjo6YmVmb3JlLFxuKjo6YWZ0ZXIge1xuXHRib3gtc2l6aW5nOiBib3JkZXItYm94O1xufVxuXG4vKiBMaW5rcyAqL1xuXG5hLCBhOmxpbmssIGE6dmlzaXRlZCAge1xuICAgIHRleHQtZGVjb3JhdGlvbjogbm9uZTtcbn1cblxuYTpob3ZlciAge1xuICAgIHRleHQtZGVjb3JhdGlvbjogbm9uZTtcbn1cblxuLyogQ29tbW9uICovXG5cbmFzaWRlLCBuYXYsIGZvb3RlciwgaGVhZGVyLCBzZWN0aW9uLCBtYWluIHtcblx0ZGlzcGxheTogYmxvY2s7XG59XG5cbmgxLCBoMiwgaDMsIGg0LCBoNSwgaDYsIHAge1xuICAgIGZvbnQtc2l6ZTogaW5oZXJpdDtcblx0Zm9udC13ZWlnaHQ6IGluaGVyaXQ7XG59XG5cbnVsLCB1bCBsaSB7XG5cdGxpc3Qtc3R5bGU6IG5vbmU7XG59XG5cbmltZyB7XG5cdHZlcnRpY2FsLWFsaWduOiB0b3A7XG59XG5cbmltZywgc3ZnIHtcblx0bWF4LXdpZHRoOiAxMDAlO1xuXHRoZWlnaHQ6IGF1dG87XG59XG5cbmFkZHJlc3Mge1xuICBmb250LXN0eWxlOiBub3JtYWw7XG59XG5cbi8qIEZvcm0gKi9cblxuaW5wdXQsIHRleHRhcmVhLCBidXR0b24sIHNlbGVjdCB7XG5cdGZvbnQtZmFtaWx5OiBpbmhlcml0O1xuICAgIGZvbnQtc2l6ZTogaW5oZXJpdDtcbiAgICBjb2xvcjogaW5oZXJpdDtcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiB0cmFuc3BhcmVudDtcbn1cblxuaW5wdXQ6Oi1tcy1jbGVhciB7XG5cdGRpc3BsYXk6IG5vbmU7XG59XG5cbmJ1dHRvbiwgaW5wdXRbdHlwZT1cInN1Ym1pdFwiXSB7XG4gICAgZGlzcGxheTogaW5saW5lLWJsb2NrO1xuICAgIGJveC1zaGFkb3c6IG5vbmU7XG4gICAgYmFja2dyb3VuZC1jb2xvcjogdHJhbnNwYXJlbnQ7XG4gICAgYmFja2dyb3VuZDogbm9uZTtcbiAgICBjdXJzb3I6IHBvaW50ZXI7XG59XG5cbmlucHV0OmZvY3VzLCBpbnB1dDphY3RpdmUsXG5idXR0b246Zm9jdXMsIGJ1dHRvbjphY3RpdmUge1xuICAgIG91dGxpbmU6IG5vbmU7XG59XG5cbmJ1dHRvbjo6LW1vei1mb2N1cy1pbm5lciB7XG5cdHBhZGRpbmc6IDA7XG5cdGJvcmRlcjogMDtcbn1cblxubGFiZWwge1xuXHRjdXJzb3I6IHBvaW50ZXI7XG59XG5cbmxlZ2VuZCB7XG5cdGRpc3BsYXk6IGJsb2NrO1xufVxuIiwiOnJvb3Qge1xuXHQtLWNvbnRhaW5lci13aWR0aDogMTIwMHB4O1xuXHQtLWNvbnRhaW5lci1wYWRkaW5nOiAxNXB4O1xuXG5cdC8vIEZvbnQgZmFtaWxpZXNcblx0LS1mb250LW1haW46ICdPbmVzdCcsIHNhbnMtc2VyaWY7XG5cdC0tZm9udC1hY2NlbnQ6ICdNYW5yb3BlJywgc2Fucy1zZXJpZjtcblx0LS1mb250LXRpdGxlczogdmFyKC0tZm9udC1tYWluKTtcblx0LS1mb250LWxlZ2FjeTogJ21haW5mb250JzsgLy8gYmFja3dhcmQgY29tcGF0aWJpbGl0eVxuXG5cdC8vIEZvbnQgd2VpZ2h0c1xuXHQtLWZvbnQtd2VpZ2h0LXRoaW46IDEwMDtcblx0LS1mb250LXdlaWdodC1leHRyYWxpZ2h0OiAyMDA7XG5cdC0tZm9udC13ZWlnaHQtbGlnaHQ6IDMwMDtcblx0LS1mb250LXdlaWdodC1yZWd1bGFyOiA0MDA7XG5cdC0tZm9udC13ZWlnaHQtbWVkaXVtOiA1MDA7XG5cdC0tZm9udC13ZWlnaHQtc2VtaWJvbGQ6IDYwMDtcblx0LS1mb250LXdlaWdodC1ib2xkOiA3MDA7XG5cdC0tZm9udC13ZWlnaHQtZXh0cmFib2xkOiA4MDA7XG5cdC0tZm9udC13ZWlnaHQtYmxhY2s6IDkwMDtcblxuXHQvLyBGb250IGNvbWJpbmF0aW9ucyAoZmFtaWx5ICsgd2VpZ2h0KVxuXHQtLWZvbnQtdGhpbjogdmFyKC0tZm9udC1tYWluKTtcblx0LS1mb250LWV4dHJhbGlnaHQ6IHZhcigtLWZvbnQtbWFpbik7XG5cdC0tZm9udC1saWdodDogdmFyKC0tZm9udC1tYWluKTtcblx0LS1mb250LXJlZ3VsYXI6IHZhcigtLWZvbnQtbWFpbik7XG5cdC0tZm9udC1tZWRpdW06IHZhcigtLWZvbnQtbWFpbik7XG5cdC0tZm9udC1zZW1pYm9sZDogdmFyKC0tZm9udC1tYWluKTtcblx0LS1mb250LWJvbGQ6IHZhcigtLWZvbnQtbWFpbik7XG5cdC0tZm9udC1leHRyYWJvbGQ6IHZhcigtLWZvbnQtbWFpbik7XG5cdC0tZm9udC1ibGFjazogdmFyKC0tZm9udC1tYWluKTtcblxuXHQtLXBhZ2UtYmc6ICNmZmY7XG5cdC0tdGV4dC1jb2xvcjogIzAwMDtcblx0LS1hY2NlbnQ6ICNhYzE4MmM7XG5cdC0tbGluay1jb2xvcjogIzI1NzhjODtcblxuXG4gICAgLS1sYXB0b3Atc2l6ZTogMTE5OXB4O1xuXHQtLXRhYmxldC1zaXplOiA5NTlweDtcblx0LS1tb2JpbGUtc2l6ZTogNTk5cHg7XG5cblx0Ly8gQ29sb3JzIHBhbGV0dGVcblx0LS1hY2NlbnQtY29sb3I6ICNGRjc1MkI7XG5cblx0Ly8gQm9yZGVycyBjb2xvcnNcblx0LS1ib3JkZXJzLWNvbG9yOiByZ2JhKDE3OCwxODgsMTk1LC41KTtcbn1cblxuLmRhcmsge1xuICAgIC0tcGFnZS1iZzogIzI1MjUyNjtcbiAgICAtLXRleHQtY29sb3I6ICNmZmY7XG59IiwiQGZvbnQtZmFjZSB7XG5cdGZvbnQtZmFtaWx5OiBGaXJhc2Fuc0Jvb2s7XG5cdGZvbnQtZGlzcGxheTogc3dhcDtcblx0c3JjOiB1cmwoXCIuLi9mb250cy9GaXJhc2Fuc0Jvb2sud29mZjJcIikgZm9ybWF0KFwid29mZjJcIiksIHVybChcIi4uL2ZvbnRzL0ZpcmFzYW5zQm9vay53b2ZmXCIpIGZvcm1hdChcIndvZmZcIik7XG5cdGZvbnQtd2VpZ2h0OiA0MDA7XG5cdGZvbnQtc3R5bGU6IG5vcm1hbDtcbn1cclxuQGZvbnQtZmFjZSB7XG5cdGZvbnQtZmFtaWx5OiBNb250c2VycmF0O1xuXHRmb250LWRpc3BsYXk6IHN3YXA7XG5cdHNyYzogdXJsKFwiLi4vZm9udHMvTW9udHNlcnJhdC1Cb2xkLndvZmYyXCIpIGZvcm1hdChcIndvZmYyXCIpLCB1cmwoXCIuLi9mb250cy9Nb250c2VycmF0LUJvbGQud29mZlwiKSBmb3JtYXQoXCJ3b2ZmXCIpO1xuXHRmb250LXdlaWdodDogNzAwO1xuXHRmb250LXN0eWxlOiBub3JtYWw7XG59XHJcbkBmb250LWZhY2Uge1xuXHRmb250LWZhbWlseTogTW9udHNlcnJhdDtcblx0Zm9udC1kaXNwbGF5OiBzd2FwO1xuXHRzcmM6IHVybChcIi4uL2ZvbnRzL01vbnRzZXJyYXQtQm9sZEl0YWxpYy53b2ZmMlwiKSBmb3JtYXQoXCJ3b2ZmMlwiKSwgdXJsKFwiLi4vZm9udHMvTW9udHNlcnJhdC1Cb2xkSXRhbGljLndvZmZcIikgZm9ybWF0KFwid29mZlwiKTtcblx0Zm9udC13ZWlnaHQ6IDQwMDtcblx0Zm9udC1zdHlsZTogbm9ybWFsO1xufVxyXG5AZm9udC1mYWNlIHtcblx0Zm9udC1mYW1pbHk6IE1vbnRzZXJyYXQ7XG5cdGZvbnQtZGlzcGxheTogc3dhcDtcblx0c3JjOiB1cmwoXCIuLi9mb250cy9Nb250c2VycmF0LVJlZ3VsYXIud29mZjJcIikgZm9ybWF0KFwid29mZjJcIiksIHVybChcIi4uL2ZvbnRzL01vbnRzZXJyYXQtUmVndWxhci53b2ZmXCIpIGZvcm1hdChcIndvZmZcIik7XG5cdGZvbnQtd2VpZ2h0OiA0MDA7XG5cdGZvbnQtc3R5bGU6IG5vcm1hbDtcbn1cclxuIiwiaHRtbCB7XG4gICAgZm9udC1zaXplOiA2Mi41JTtcbiAgICBoZWlnaHQ6IDEwMCU7XG4gICAgd2lkdGg6IDEwMCU7XG5cdHNjcm9sbC1iZWhhdmlvcjogc21vb3RoO1xuXG5cdC8vIFJlc3BvbnNpdmUgZm9udCBzY2FsaW5nXG5cdEBtZWRpYSAobWF4LXdpZHRoOiAxNTAwcHgpIHtcblx0XHRmb250LXNpemU6IDYyLjUlOyAvLyAxcmVtID0gMTBweFxuXHR9XG5cdEBtZWRpYSAobWF4LXdpZHRoOiA5OTJweCkge1xuXHRcdGZvbnQtc2l6ZTogNjAlOyAvLyAxcmVtID0gOS42cHggKHNsaWdodGx5IHNtYWxsZXIgZm9yIHRhYmxldHMpXG5cdH1cblx0QG1lZGlhIChtYXgtd2lkdGg6IDc2N3B4KSB7XG5cdFx0Zm9udC1zaXplOiA1OCU7IC8vIDFyZW0gPSA5LjI4cHggKHNtYWxsZXIgZm9yIG1vYmlsZSlcblx0fVxuXHRAbWVkaWEgKG1heC13aWR0aDogNTc2cHgpIHtcblx0XHRmb250LXNpemU6IDU2JTsgLy8gMXJlbSA9IDguOTZweCAoc21hbGxlc3QgZm9yIHNtYWxsIG1vYmlsZSlcblx0fVxufVxuXG5ib2R5IHtcblx0YmFja2dyb3VuZC1jb2xvcjogdmFyKC0tYmdyLWNvbG9yX19kYXJrLWdyYXktb3JpZ2luYWwpO1xuXHRjb2xvcjogdmFyKC0tdGV4dF9fb24tZGFyayk7XG4gICAgZm9udC1mYW1pbHk6IHZhcigtLWZvbnQtbWFpbik7XG4gICAgZm9udC1zaXplOiAxLjZyZW07XG4gICAgQG1lZGlhIChtYXgtd2lkdGg6IDc2N3B4KSB7XG4gICAgICAgIGZvbnQtc2l6ZTogMS40cmVtO1xuICAgIH1cbiAgICBAbWVkaWEgKG1heC13aWR0aDogNTc2cHgpIHtcbiAgICAgICAgZm9udC1zaXplOiAxLjJyZW07XG4gICAgfVxufVxuXG4uYnRuIHtcbiAgICBkaXNwbGF5OiBpbmxpbmUtZmxleDtcbn1cbi5idG4tbm9ybWFsIC5idG4tY2xpY2sge1xuICAgIC13ZWJraXQtYm94LXNoYWRvdzogaW5zZXQgMHB4IDBweCAwcHggMXB4IHZhcigtLWJvcmRlcnNfX29uLWRhcmspO1xuICAgIGJveC1zaGFkb3c6IGluc2V0IDBweCAwcHggMHB4IDFweCB2YXIoLS1ib3JkZXJzX19vbi1kYXJrKTtcbn1cbi5idG4tbm9ybWFsIC5idG4tY2xpY2std2hpdGUge1xuICAgIC13ZWJraXQtYm94LXNoYWRvdzogaW5zZXQgMHB4IDBweCAwcHggMXB4IHZhcigtLWhlYWRlcnNfX29uLWRhcmspO1xuICAgIGJveC1zaGFkb3c6IGluc2V0IDBweCAwcHggMHB4IDFweCB2YXIoLS1oZWFkZXJzX19vbi1kYXJrKTtcbn1cbi5idG4tY2xpY2sge1xuICAgIGN1cnNvcjogcG9pbnRlcjtcbiAgICBib3JkZXI6IDA7XG4gICAgY29sb3I6IHZhcigtLXRleHRfX29uLWRhcmspO1xuICAgIGJhY2tncm91bmQ6IHRyYW5zcGFyZW50O1xuICAgIGJvcmRlci1yYWRpdXM6IDEwcmVtICFpbXBvcnRhbnQ7XG4gICAgbWluLXdpZHRoOiAxcmVtO1xuICAgIG92ZXJmbG93OiBoaWRkZW47XG4gICAgZGlzcGxheTogZmxleDtcbiAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICAgIHBvc2l0aW9uOiByZWxhdGl2ZTtcbiAgICB0ZXh0LWRlY29yYXRpb246IG5vbmU7XG4gICAgd2lsbC1jaGFuZ2U6IHRyYW5zZm9ybTtcbiAgICBvdXRsaW5lOiAwO1xuICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWigwKSByb3RhdGUoMC4wMDFkZWcpO1xuXHRwYWRkaW5nOiAxLjdyZW0gM3JlbSAhaW1wb3J0YW50O1xuXHRtYXJnaW46IDAgIWltcG9ydGFudDtcblx0dGV4dC1kZWNvcmF0aW9uOiBub25lICFpbXBvcnRhbnQ7XG4gICAgZm9udC1zaXplOiAxLjRyZW07XG5cbiAgICBAbWVkaWEgKG1heC13aWR0aDogOTkycHgpIHtcblx0XHRmb250LXNpemU6IDEuMnJlbTtcbiAgICAgICAgaGVpZ2h0OiA0LjhyZW07XG4gICAgICAgIHBhZGRpbmc6IDEuN3JlbSAycmVtICFpbXBvcnRhbnQ7XG5cdH1cbn1cbi5idG4tY2xpY2stc21hbGwtcGFkZGluZyB7XG5cdHBhZGRpbmc6IDFyZW0gMS41cmVtICFpbXBvcnRhbnQ7XG59XG5cbi5idG4tY2xpY2std2hpdGUge1xuICAgIGN1cnNvcjogcG9pbnRlcjtcbiAgICBib3JkZXI6IDA7XG4gICAgY29sb3I6IHZhcigtLWhlYWRlcnNfX29uLWRhcmspO1xuICAgIGJhY2tncm91bmQ6IHRyYW5zcGFyZW50O1xuICAgIGJvcmRlci1yYWRpdXM6IDEwcmVtICFpbXBvcnRhbnQ7XG4gICAgbWluLXdpZHRoOiAxcmVtO1xuICAgIG92ZXJmbG93OiBoaWRkZW47XG4gICAgZGlzcGxheTogZmxleDtcbiAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICAgIHBvc2l0aW9uOiByZWxhdGl2ZTtcbiAgICB0ZXh0LWRlY29yYXRpb246IG5vbmU7XG4gICAgd2lsbC1jaGFuZ2U6IHRyYW5zZm9ybTtcbiAgICBvdXRsaW5lOiAwO1xuICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWigwKSByb3RhdGUoMC4wMDFkZWcpO1xuXHRwYWRkaW5nOiAxLjdyZW0gM3JlbSAhaW1wb3J0YW50O1xuXHRtYXJnaW46IDAgIWltcG9ydGFudDtcblx0dGV4dC1kZWNvcmF0aW9uOiBub25lICFpbXBvcnRhbnQ7XG4gICAgZm9udC1zaXplOiAxLjRyZW07XG5cbiAgICBAbWVkaWEgKG1heC13aWR0aDogOTkycHgpIHtcblx0XHRmb250LXNpemU6IDEuMnJlbVxuXHR9XG59XG5cblxuLmJ0bi1iZ3ItZGFyayB7XG4gICAgYmFja2dyb3VuZDogdmFyKC0tYmdyLWNvbG9yX19kYXJrLWdyYXktb3JpZ2luYWwpO1xufVxuLmJ0bi1iZ3Itd2hpdGUge1xuICAgIGJhY2tncm91bmQ6IHZhcigtLWhlYWRlcnNfX29uLWRhcmspO1xufVxuLmJ0bi1maWxsIHtcbiAgICBiYWNrZ3JvdW5kOiB2YXIoLS1ib3JkZXJzX19vbi1kYXJrKTtcbiAgICBwb3NpdGlvbjogYWJzb2x1dGU7XG4gICAgd2lkdGg6IDE1MCU7XG4gICAgaGVpZ2h0OiAyMDAlO1xuICAgIGJvcmRlci1yYWRpdXM6IDUwJTtcbiAgICB0b3A6IC01MCU7XG4gICAgbGVmdDogLTI1JTtcbiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZTNkKDAsLTc2JSwwKTtcbiAgICB3aWxsLWNoYW5nZTogdHJhbnNmb3JtO1xuICAgIHRyYW5zaXRpb246IGJhY2tncm91bmQtY29sb3IgZWFzZS1pbi1vdXQgLjI1cztcbn1cbi5idG4tdGV4dCB7XG4gICAgZGlzcGxheTogZmxleDtcbiAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICAgIHdpZHRoOiAxMDAlO1xuICAgIGhlaWdodDogMTAwJTtcbiAgICB6LWluZGV4OiAyO1xuICAgIGNvbG9yOiB2YXIoLS10ZXh0X19vbi1kYXJrKTtcbiAgICBwb3NpdGlvbjogcmVsYXRpdmU7XG4gICAgdHJhbnNmb3JtOiByb3RhdGUoMC4wMDFkZWcpO1xuICAgIHBvaW50ZXItZXZlbnRzOiBub25lO1xuICAgIHdpbGwtY2hhbmdlOiB0cmFuc2Zvcm0sIGNvbG9yO1xufVxuLmJ0bi1ub3JtYWwgLmJ0bi10ZXh0IC5idG4tdGV4dC1pbm5lciB7XG4gICAgY29sb3I6IHZhcigtLWhlYWRlcnNfX29uLWRhcmspICFpbXBvcnRhbnQ7XG5cdGRpc3BsYXk6IGZsZXg7XG4gICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICBnYXA6IDFyZW07XG4gICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG59XG4ucG9pbnRlcm5vbmUge1xuICAgIHBvaW50ZXItZXZlbnRzOiBub25lO1xufVxuLnppbmRleCB7XG4gICAgei1pbmRleDogOTk5OTk5OTk5OTk5OTtcbiAgICBwb2ludGVyLWV2ZW50czogYWxsO1xufVxuXG4uYnRuLXRleHQtaW5uZXIge1xuXHRpbWcge1xuXHRcdHdpZHRoOiAyLjRyZW07XHRcblx0fVxufVxuQG1lZGlhIChtYXgtd2lkdGg6IDk5Mikge1xuXHQuYnRuLXRleHQtaW5uZXIgeyBpbWcgeyB3aWR0aDogMS40cmVtOyB9IH1cbn1cbkBtZWRpYSAobWluLXdpZHRoOiA5OTMpIHtcblx0LmJ0bi10ZXh0LWlubmVyIHsgaW1nIHsgd2lkdGg6IDEuNXJlbTsgfSB9XG59IFxuQG1lZGlhIChtYXgtd2lkdGg6IDE0MDApIHtcblx0LmJ0bi10ZXh0LWlubmVyIHsgaW1nIHsgd2lkdGg6IDEuNnJlbTsgfSB9XG59XG4udXBwZXJjYXNlIHtcblx0dGV4dC10cmFuc2Zvcm06IHVwcGVyY2FzZTtcbn1cblxuXG4ubWFpbi1idXR0b24ge1xuICAgIGZvbnQtc2l6ZTogMS40cmVtO1xuICAgIGNvbG9yOiB2YXIoLS1oZWFkZXJzX19vbi1kYXJrKTtcbiAgICBiYWNrZ3JvdW5kOiB2YXIoLS1iZ3ItY29sb3JfX2RhcmstZ3JheS1vcmlnaW5hbCk7XG4gICAgcGFkZGluZzogMC4zcmVtIDAuM3JlbSAwLjNyZW0gMi41cmVtO1xuICAgIGJvcmRlcjogMXB4IHNvbGlkIHZhcigtLWhlYWRlcnNfX29uLWRhcmspO1xuICAgIGJvcmRlci1yYWRpdXM6IDUwcmVtO1xuICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgaGVpZ2h0OiA1LjhyZW07XG4gICAgZGlzcGxheTogZmxleDtcbiAgICBqdXN0aWZ5LWNvbnRlbnQ6IGZsZXgtc3RhcnQ7XG4gICAgcG9zaXRpb246IHJlbGF0aXZlO1xuICAgIHotaW5kZXg6IDE7XG5cbiAgICBAbWVkaWEgKG1heC13aWR0aDogOTkycHgpIHtcblx0XHRmb250LXNpemU6IDEuMnJlbTtcbiAgICAgICAgcGFkZGluZzogMC4zcmVtIDAuM3JlbSAwLjNyZW0gMC45cmVtO1xuICAgICAgICBoZWlnaHQ6IDQuNnJlbTtcblx0fVxuXG4gICAgXG4gICAgJl9faWNvbiB7XG4gICAgICAgIGJvcmRlci1yYWRpdXM6IDEwcmVtO1xuICAgICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTgwZGVnLCAjRkZGIDAlLCAjQkFCQUJBIDEwMCUpO1xuICAgICAgICBib3gtc2hhZG93OiAtMXB4IDBweCAxMC4zcHggMHB4IHJnYmEoMCwgMCwgMCwgMC4xMSk7XG4gICAgICAgIHdpZHRoOiA1LjJyZW07XG4gICAgICAgIGhlaWdodDogNS4ycmVtO1xuICAgICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGVuZDtcbiAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgICAgbWFyZ2luLWxlZnQ6IDEuNXJlbTtcbiAgICAgICAgcG9zaXRpb246IGFic29sdXRlO1xuICAgICAgICByaWdodDogMC4ycmVtO1xuICAgICAgICB0cmFuc2l0aW9uOiB2YXIoLS1hbmltYXRpb24tc21vb3RoKTtcbiAgICAgICAgei1pbmRleDogLTE7XG5cbiAgICAgICAgJiBpbWcge1xuICAgICAgICAgICAgaGVpZ2h0OiA1MCU7XG4gICAgICAgICAgICBwYWRkaW5nLXJpZ2h0OiBjYWxjKDIuNnJlbSAvIDIpO1xuXG4gICAgICAgICAgICBAbWVkaWEgKG1heC13aWR0aDogOTkycHgpIHtcbiAgICAgICAgICAgICAgICBwYWRkaW5nLXJpZ2h0OiBjYWxjKDJyZW0gLyAyKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuXG4gICAgICAgIEBtZWRpYSAobWF4LXdpZHRoOiA5OTJweCkge1xuICAgICAgICAgICAgd2lkdGg6IDRyZW07XG4gICAgICAgICAgICBoZWlnaHQ6IDRyZW07XG4gICAgICAgICAgICBtYXJnaW4tbGVmdDogMXJlbTtcbiAgICAgICAgfVxuICAgIH1cbiAgICAmOmhvdmVyIHtcbiAgICAgICAgY29sb3I6IHZhcigtLWJvcmRlcnNfX29uLWRhcmspO1xuICAgIH1cbiAgICAmOmhvdmVyICZfX2ljb24ge1xuICAgICAgICB3aWR0aDogY2FsYygxMDAlIC0gLjRyZW0pO1xuICAgIH1cblxuICAgICZfX2R1bW15IHtcbiAgICAgICAgdmlzaWJpbGl0eTogaGlkZGVuO1xuICAgICAgICB3aWR0aDogNS4ycmVtO1xuICAgICAgICBoZWlnaHQ6IDUuMnJlbTtcbiAgICAgICAgbWFyZ2luLWxlZnQ6IDEuNXJlbTtcblxuICAgICAgICBAbWVkaWEgKG1heC13aWR0aDogOTkycHgpIHtcbiAgICAgICAgICAgIHdpZHRoOiA0cmVtO1xuICAgICAgICAgICAgaGVpZ2h0OiA0cmVtO1xuICAgICAgICAgICAgbWFyZ2luLWxlZnQ6IDFyZW07XG4gICAgICAgIH1cbiAgICB9XG59XG5cbjo6LW1vei1zZWxlY3Rpb24ge1xuICAgIGJhY2tncm91bmQtY29sb3I6IHZhcigtLWJvcmRlcnNfX29uLWRhcmspO1xuICAgIGNvbG9yOiB2YXIoLS1oZWFkZXJzX19vbi1kYXJrKTtcbn1cblxuOjpzZWxlY3Rpb24ge1xuICAgIGJhY2tncm91bmQtY29sb3I6IHZhcigtLWJvcmRlcnNfX29uLWRhcmspO1xuICAgIGNvbG9yOiB2YXIoLS1oZWFkZXJzX19vbi1kYXJrKTtcbn0iLCIuZG9jcyB7XG5cdGRpc3BsYXk6IGdyaWQ7XG5cdC8vIHJvdy1nYXA6IDQwcHg7XG5cdGxpbmUtaGVpZ2h0OiAxLjU7XG5cblx0cCB7XG5cdFx0bWFyZ2luOiAxcmVtIDA7XG5cdH1cblxuXHR1bCxcblx0b2wge1xuXHRcdHBhZGRpbmctbGVmdDogMnJlbTtcblxuXHRcdGxpIHtcblx0XHRcdGxpc3Qtc3R5bGU6IGRpc2M7XG5cdFx0XHRtYXJnaW4tYm90dG9tOiAwLjVyZW07XG5cdFx0fVxuXHR9XG5cblx0b2wgbGkge1xuXHRcdGxpc3Qtc3R5bGU6IGRlY2ltYWw7XG5cdH1cblxuXHRzZWN0aW9uLCBzZWN0aW9uLmRvY3Mge1xuXHRcdHBhZGRpbmc6IDQwcHggMDtcblx0fVxuXG5cdHNlY3Rpb24gKyBzZWN0aW9uIHtcblx0XHRib3JkZXItdG9wOiAxcHggc29saWQgI2RhZTVlOTtcblx0fVxuXG5cdHNtYWxsIHtcblx0XHRmb250LXNpemU6IDFyZW07XG5cdFx0Y29sb3I6IHJnYigxNzIsIDE3MiwgMTcyKTtcblx0fVxuXG5cdC50aXRsZS0xOmZpcnN0LWNoaWxkLFxuXHQudGl0bGUtMjpmaXJzdC1jaGlsZCB7XG5cdFx0bWFyZ2luLXRvcDogMCAhaW1wb3J0YW50O1xuXHR9XG59XG5cbi50ZXN0IHtcblx0d2lkdGg6IDYwMHB4O1xuXHRoZWlnaHQ6IDMwMHB4O1xuXHRtYXJnaW46IDUwcHggYXV0bztcblx0YmFja2dyb3VuZC1jb2xvcjogIzk5OTtcblx0YmFja2dyb3VuZC1wb3NpdGlvbjogY2VudGVyIGNlbnRlcjsgLyogeCB5ICovXG5cdGJhY2tncm91bmQtc2l6ZTogY292ZXI7XG5cdGJhY2tncm91bmQtcmVwZWF0OiBuby1yZXBlYXQ7XG5cblx0Ly8g0KDQsNCx0L7RgtCw0LXRgiByZXRpbmEgc2NzcyB3ZWJwXG5cdC8vINCz0YDRg9C30LjRgtGB0Y8gMngganBlZyAyeCB3ZWJwXG5cdGJhY2tncm91bmQtaW1hZ2U6IHVybCgnLi8uLi9pbWcvcHJvamVjdC0wMi5qcGcnKTtcblx0QGluY2x1ZGUgbWVkaWFCZygpIHtcblx0XHRiYWNrZ3JvdW5kLWltYWdlOiB1cmwoJy4vLi4vaW1nL3Byb2plY3QtMDJAMnguanBnJyk7XG5cdH1cblxuXHQvLyDQndC1INGA0LDQsdC+0YLQsNC10YIgd2VicCBjINC/0L7QtNGB0YLQsNC90L7QstC60L7QuSAyeCwg0LPRgNGD0LfQuNGC0YHRjyBqcGcgMnhcblx0Ly8gYmFja2dyb3VuZC1pbWFnZTogaW1hZ2Utc2V0KFxuXHQvLyBcdHVybCgnLi8uLi9pbWcvcHJvamVjdC0wMi5qcGcnKSAxeCxcblx0Ly8gXHR1cmwoJy4vLi4vaW1nL3Byb2plY3QtMDJAMnguanBnJykgMngpO1xufVxuXG4udGVzdC0yIHtcblx0d2lkdGg6IDYwMHB4O1xuXHRoZWlnaHQ6IDMwMHB4O1xuXHRtYXJnaW46IDUwcHggYXV0bztcblx0YmFja2dyb3VuZC1jb2xvcjogIzk5OTtcblx0YmFja2dyb3VuZC1wb3NpdGlvbjogY2VudGVyIGNlbnRlcjsgLyogeCB5ICovXG5cdGJhY2tncm91bmQtc2l6ZTogY292ZXI7XG5cdGJhY2tncm91bmQtcmVwZWF0OiBuby1yZXBlYXQ7XG5cblx0Ly8g0J3QtSDRgNCw0LHQvtGC0LDQtdGCIHdlYnAgYyDQv9C+0LTRgdGC0LDQvdC+0LLQutC+0LkgMngsINCz0YDRg9C30LjRgtGB0Y8ganBnIDJ4XG5cdGJhY2tncm91bmQtaW1hZ2U6IGltYWdlLXNldChcblx0XHR1cmwoJy4vLi4vaW1nL3Byb2plY3QtMDIuanBnJykgMXgsXG5cdFx0dXJsKCcuLy4uL2ltZy9wcm9qZWN0LTAyQDJ4LmpwZycpIDJ4KTtcbn1cblxuLmZvbnQtMSB7XG5cdGZvbnQtZmFtaWx5OiAnTW9udHNlcnJhdCc7XG5cdGZvbnQtd2VpZ2h0OiA3MDA7XG5cdGZvbnQtc3R5bGU6IGl0YWxpYztcbn1cblxuLmZvbnQtMiB7XG5cdGZvbnQtZmFtaWx5OiAnRmlyYXNhbnNCb29rJztcblx0Zm9udC13ZWlnaHQ6IDQwMDtcbn1cbiIsIkBtaXhpbiBtZWRpYUJnKCkge1xuXHRAbWVkaWEgKC13ZWJraXQtbWluLWRldmljZS1waXhlbC1yYXRpbzogMiksIChtaW4tcmVzb2x1dGlvbjogMTkyZHBpKSB7XG5cdCAgICBAY29udGVudDtcbiAgICB9XG59XG5cbkBtaXhpbiB0YWJsZXQgKCkge1xuICAgIEBtZWRpYSAobWF4LXdpZHRoOiAxMjIwcHgpIHtcblx0XHRAY29udGVudDtcblx0fVxufVxuXG5AbWl4aW4gbW9iaWxlICgpIHtcbiAgICBAbWVkaWEgKG1heC13aWR0aDogODIwcHgpIHtcblx0XHRAY29udGVudDtcblx0fVxufSIsIi5ub25lIHtcblx0ZGlzcGxheTogbm9uZSAhaW1wb3J0YW50O1xufVxuXG4udmlzdWFsbHktaGlkZGVuIHtcblx0cG9zaXRpb246IGFic29sdXRlO1xuXHR3aWR0aDogMXB4O1xuXHRoZWlnaHQ6IDFweDtcblx0bWFyZ2luOiAtMXB4O1xuXHRib3JkZXI6IDA7XG5cdHBhZGRpbmc6IDA7XG5cdHdoaXRlLXNwYWNlOiBub3dyYXA7XG5cdGNsaXAtcGF0aDogaW5zZXQoMTAwJSk7XG5cdGNsaXA6IHJlY3QoMCAwIDAgMCk7XG5cdG92ZXJmbG93OiBoaWRkZW47XG59XG5cbi5uby1zY3JvbGwge1xuXHRvdmVyZmxvdy15OiBoaWRkZW47XG59XG5cbi50ZXh0LWxlZnQge1xuXHR0ZXh0LWFsaWduOiBsZWZ0O1xufVxuXG4udGV4dC1yaWdodCB7XG5cdHRleHQtYWxpZ246IHJpZ2h0O1xufVxuXG4udGV4dC1jZW50ZXIge1xuXHR0ZXh0LWFsaWduOiBjZW50ZXI7XG59XG5cbi5kLWZsZXgge1xuXHRkaXNwbGF5OiBmbGV4O1xufVxuXG4uZmxleC1jZW50ZXIge1xuXHRqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbn1cbiIsIi5jb250YWluZXIge1xuXHRtYXJnaW46IDAgYXV0bztcblx0Ym9yZGVyLWxlZnQ6MXB4IHNvbGlkIHZhcigtLWJvcmRlcnNfX29uLWRhcmspO1xuXHRib3JkZXItcmlnaHQ6MXB4IHNvbGlkIHZhcigtLWJvcmRlcnNfX29uLWRhcmspO1xuXG5cdEBtZWRpYSAobWluLXdpZHRoOiAxNzAwcHgpIHtcblx0XHRtYXJnaW46YXV0byA4cmVtO1xuXHRcdHBhZGRpbmc6IDAgNXJlbTtcblx0fVxuXHRAbWVkaWEgKG1pbi13aWR0aDogOTkzcHgpIGFuZCAobWF4LXdpZHRoOjE2OTlweCkge1xuXHRcdG1hcmdpbjphdXRvIDVyZW07XG5cdFx0cGFkZGluZzogMCAzcmVtO1xuXHR9XG5cdEBtZWRpYSAobWF4LXdpZHRoOiA5OTJweCkge1xuXHRcdG1hcmdpbjphdXRvIDNyZW07XG5cdFx0cGFkZGluZzogMCAycmVtO1xuXHR9XG5cdEBtZWRpYSAobWF4LXdpZHRoOiA3NjdweCkge1xuXHRcdG1hcmdpbjphdXRvIDJyZW07XG5cdFx0cGFkZGluZzogMCAycmVtO1xuXHR9XG5cdEBtZWRpYSAobWF4LXdpZHRoOiA1NzZweCkge1xuXHRcdG1hcmdpbjphdXRvIDEuNnJlbTtcblx0XHRwYWRkaW5nOiAwIDEuNnJlbTtcblx0fVxufVxuXG4uY29udGFpbmVyLWdyaWQtc2VjdGlvbiB7XG5cdG1hcmdpbjogMCBhdXRvO1xuXHRoZWlnaHQ6IDEwMCU7XG5cdG1pbi1oZWlnaHQ6IGluaGVyaXQ7XG5cdGJvcmRlci1sZWZ0OjFweCBzb2xpZCB2YXIoLS1ib3JkZXJzX19vbi1kYXJrKTtcblx0Ym9yZGVyLXJpZ2h0OjFweCBzb2xpZCB2YXIoLS1ib3JkZXJzX19vbi1kYXJrKTtcblx0cG9zaXRpb246IHJlbGF0aXZlO1xuXHR6LWluZGV4OiAxO1xuXHRvdmVyZmxvdzogaGlkZGVuO1xuXG5cdGRpc3BsYXk6IGZsZXg7XG4gICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47XG5cblx0QG1lZGlhIChtaW4td2lkdGg6IDE3MDBweCkge1xuXHRcdG1hcmdpbjphdXRvIDhyZW07XG5cdH1cblx0QG1lZGlhIChtaW4td2lkdGg6IDk5M3B4KSBhbmQgKG1heC13aWR0aDoxNjk5cHgpIHtcblx0XHRtYXJnaW46YXV0byA1cmVtO1xuXHR9XG5cdEBtZWRpYSAobWF4LXdpZHRoOiA5OTJweCkge1xuXHRcdG1hcmdpbjphdXRvIDNyZW07XG5cdH1cblx0QG1lZGlhIChtYXgtd2lkdGg6IDc2N3B4KSB7XG5cdFx0bWFyZ2luOmF1dG8gMnJlbTtcblx0fVxuXHRAbWVkaWEgKG1heC13aWR0aDogNTc2cHgpIHtcblx0XHRtYXJnaW46YXV0byAxLjZyZW07XG5cdH1cbn1cblxuLmNvbnRhaW5lci1uby1ib3JkZXIge1xuXHRtYXJnaW46IDAgYXV0bztcblxuXHRAbWVkaWEgKG1pbi13aWR0aDogMTcwMHB4KSB7XG5cdFx0bWFyZ2luOmF1dG8gOHJlbTtcblx0XHRwYWRkaW5nOiAwIDVyZW07XG5cdH1cblx0QG1lZGlhIChtaW4td2lkdGg6IDk5M3B4KSBhbmQgKG1heC13aWR0aDoxNjk5cHgpIHtcblx0XHRtYXJnaW46YXV0byA1cmVtO1xuXHRcdHBhZGRpbmc6IDAgM3JlbTtcblx0fVxuXHRAbWVkaWEgKG1heC13aWR0aDogOTkycHgpIHtcblx0XHRtYXJnaW46YXV0byAzcmVtO1xuXHRcdHBhZGRpbmc6IDAgMnJlbTtcblx0fVxuXHRAbWVkaWEgKG1heC13aWR0aDogNzY3cHgpIHtcblx0XHRtYXJnaW46YXV0byAycmVtO1xuXHRcdHBhZGRpbmc6IDAgMnJlbTtcblx0fVxuXHRAbWVkaWEgKG1heC13aWR0aDogNTc2cHgpIHtcblx0XHRtYXJnaW46YXV0byAxLjZyZW07XG5cdFx0cGFkZGluZzogMCAxLjZyZW07XG5cdH1cbn1cblxuLmNvbnRhaW5lci1pbnNpZGUtY29udGFpbmVyIHtcblx0cG9zaXRpb246IHJlbGF0aXZlO1xuXG5cdEBtZWRpYSAobWluLXdpZHRoOiAxNzAwcHgpIHtcblx0XHRwYWRkaW5nOiAwIDVyZW07XG5cdH1cblx0QG1lZGlhIChtaW4td2lkdGg6IDk5M3B4KSBhbmQgKG1heC13aWR0aDoxNjk5cHgpIHtcblx0XHRwYWRkaW5nOiAwIDNyZW07XG5cdH1cblx0QG1lZGlhIChtYXgtd2lkdGg6IDk5MnB4KSB7XG5cdFx0cGFkZGluZzogMCAycmVtO1xuXHR9XG5cdEBtZWRpYSAobWF4LXdpZHRoOiA3NjdweCkge1xuXHRcdHBhZGRpbmc6IDAgMnJlbTtcblx0fVxuXHRAbWVkaWEgKG1heC13aWR0aDogNTc2cHgpIHtcblx0XHRwYWRkaW5nOiAwIDEuNnJlbTtcblx0fVxufVxuXG4ubGluZXMge1xuXHRwb2ludGVyLWV2ZW50czogbm9uZTtcblx0cG9zaXRpb246IGFic29sdXRlO1xuXHR6LWluZGV4OiAtNTtcbiAgICBiYWNrZ3JvdW5kOiB0cmFuc3BhcmVudDtcbiAgICB3aWR0aDogMTAwJTtcbiAgICBoZWlnaHQ6IDEwMCU7XG4gICAgb3BhY2l0eTogMTtcbiAgICBkaXNwbGF5OiBmbGV4O1xuICAgIGZsZXgtZ3JvdzogMDtcbiAgICBmbGV4LXNocmluazogMDtcbiAgICBmbGV4LXdyYXA6IG5vd3JhcDtcblxuICAgICZfX2VtcHR5e1xuICAgICAgICB3aWR0aDogY2FsYygyNSUgLSAxcHgpO1xuICAgICAgICBiYWNrZ3JvdW5kOiB0cmFuc3BhcmVudDtcbiAgICAgICAgaGVpZ2h0OiAxMDAlO1xuICAgICAgICBvcGFjaXR5OiAuMTtcblxuICAgICAgICBAbWVkaWEgKG1heC13aWR0aDogOTkycHgpIHtcbiAgICAgICAgICAgIHdpZHRoOiBjYWxjKDMzLjMzMzMzJSAtIDFweCk7XG4gICAgICAgIH1cbiAgICB9XG4gICAgJl9fbGluZSB7XG4gICAgICAgIHdpZHRoOiAxcHg7XG4gICAgICAgIGJhY2tncm91bmQtY29sb3I6IHZhcigtLWJvcmRlcnNfX29uLWRhcmspO1xuICAgICAgICBoZWlnaHQ6IDEwMCU7XG4gICAgfVxuXG5cdCZfX2xpbmUtLWludmlzaWJsZSB7XG5cdFx0d2lkdGg6IDFweDtcbiAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogdHJhbnNwYXJlbnQ7XG4gICAgICAgIGhlaWdodDogMTAwJTtcblx0fVxufVxuXG5cbi5saW5lcy1tZC0yIHtcblx0cG9pbnRlci1ldmVudHM6IG5vbmU7XG5cdHBvc2l0aW9uOiBhYnNvbHV0ZTtcblx0ei1pbmRleDogLTU7XG4gICAgYmFja2dyb3VuZDogdHJhbnNwYXJlbnQ7XG4gICAgd2lkdGg6IDEwMCU7XG4gICAgaGVpZ2h0OiAxMDAlO1xuICAgIG9wYWNpdHk6IDE7XG4gICAgZGlzcGxheTogZmxleDtcbiAgICBmbGV4LWdyb3c6IDA7XG4gICAgZmxleC1zaHJpbms6IDA7XG4gICAgZmxleC13cmFwOiBub3dyYXA7XG5cbiAgICAmX19lbXB0eXtcbiAgICAgICAgd2lkdGg6IGNhbGMoMjUlIC0gMXB4KTtcbiAgICAgICAgYmFja2dyb3VuZDogdHJhbnNwYXJlbnQ7XG4gICAgICAgIGhlaWdodDogMTAwJTtcbiAgICAgICAgb3BhY2l0eTogLjE7XG5cbiAgICAgICAgQG1lZGlhIChtYXgtd2lkdGg6IDk5MnB4KSB7XG4gICAgICAgICAgICB3aWR0aDogY2FsYyg1MCUgLSAxcHgpO1xuICAgICAgICB9XG4gICAgfVxuICAgICZfX2xpbmUge1xuICAgICAgICB3aWR0aDogMXB4O1xuICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiB2YXIoLS1ib3JkZXJzX19vbi1kYXJrKTtcbiAgICAgICAgaGVpZ2h0OiAxMDAlO1xuICAgIH1cbn1cblxuLm1haW4tbWVudS1vcGVuIC5jb250YWluZXIge1xuXHRib3JkZXItbGVmdDogMXB4IHNvbGlkIHJnYmEoMjU1LCAwLCAwLCAwKTtcblx0Ym9yZGVyLXJpZ2h0OiAxcHggc29saWQgcmdiYSgyNTUsIDAsIDAsIDApO1xufVxuLm1haW4tbWVudS1vcGVuIC5jb250YWluZXIge1xuXG59XG4uZGlzYWJsZS1wYWRkaW5nIHtcblx0cGFkZGluZzogMCAhaW1wb3J0YW50O1xufVxuLmJvcmRlcnMtbC1yIHtcblx0Ym9yZGVyLWxlZnQ6MXB4IHNvbGlkIHZhcigtLWJvcmRlcnNfX29uLWRhcmspO1xuXHRib3JkZXItcmlnaHQ6MXB4IHNvbGlkIHZhcigtLWJvcmRlcnNfX29uLWRhcmspO1xufVxuLmJvcmRlcnMtdCB7XG5cdGJvcmRlci10b3A6MXB4IHNvbGlkIHZhcigtLWJvcmRlcnNfX29uLWRhcmspO1xufVxuLmJvcmRlcnMtYiB7XG5cdGJvcmRlci1ib3R0b206MXB4IHNvbGlkIHZhcigtLWJvcmRlcnNfX29uLWRhcmspO1xufVxuLmJvcmRlcnMtdC1iIHtcblx0Ym9yZGVyLXRvcDoxcHggc29saWQgdmFyKC0tYm9yZGVyc19fb24tZGFyayk7XG5cdGJvcmRlci1ib3R0b206MXB4IHNvbGlkIHZhcigtLWJvcmRlcnNfX29uLWRhcmspO1xufVxuLmJvcmRlcnMtYWxsIHtcblx0Ym9yZGVyOjFweCBzb2xpZCB2YXIoLS1ib3JkZXJzX19vbi1kYXJrKTtcbn1cbi5kaXNhYmxlLWJvcmRlcnMge1xuXHRib3JkZXI6IG5vbmUgIWltcG9ydGFudDtcbn1cblxuXG5cbi5mbGV4IHtcbiAgICBkaXNwbGF5OiAtd2Via2l0LWJveDtcbiAgICBkaXNwbGF5OiAtbXMtZmxleGJveDtcbiAgICBkaXNwbGF5OiBmbGV4XG59XG5cbi5mbGV4X2Z3IHtcbiAgICAtbXMtZmxleC13cmFwOiB3cmFwO1xuICAgIGZsZXgtd3JhcDogd3JhcFxufVxuXG4uZmxleF9haWMge1xuICAgIC13ZWJraXQtYm94LWFsaWduOiBjZW50ZXI7XG4gICAgLW1zLWZsZXgtYWxpZ246IGNlbnRlcjtcbiAgICBhbGlnbi1pdGVtczogY2VudGVyXG59XG5cbi5mbGV4X2FpZmUge1xuICAgIC13ZWJraXQtYm94LWFsaWduOiBlbmQ7XG4gICAgLW1zLWZsZXgtYWxpZ246IGVuZDtcbiAgICBhbGlnbi1pdGVtczogZmxleC1lbmRcbn1cblxuLmZsZXhfamNjIHtcbiAgICAtd2Via2l0LWJveC1wYWNrOiBjZW50ZXI7XG4gICAgLW1zLWZsZXgtcGFjazogY2VudGVyO1xuICAgIGp1c3RpZnktY29udGVudDogY2VudGVyXG59XG5cbi5mbGV4X2pjc2Ige1xuICAgIC13ZWJraXQtYm94LXBhY2s6IGp1c3RpZnk7XG4gICAgLW1zLWZsZXgtcGFjazoganVzdGlmeTtcbiAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW5cbn1cbkBtZWRpYSAobWF4LXdpZHRoOjc2N3B4KSB7XG4uZmxleF9tb2JpbGUtY29sdW1uIHtcblx0ZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbn1cbn1cblxuXG5cbi5jb250YWluZXItcmlnaHQge1xuXHQvKiBvdmVyZmxvdy14OiBoaWRkZW47ICovXG5cdHBhZGRpbmctbGVmdDogY2FsYyhcblx0XHQoMTAwJSAtIHZhcigtLWNvbnRhaW5lci13aWR0aCkpIC8gMiArIHZhcigtLWNvbnRhaW5lci1wYWRkaW5nKVxuXHQpO1xuXG5cdEBtZWRpYSAobWF4LXdpZHRoOiB2YXIoLS1sYXB0b3Atc2l6ZSkpIHtcblx0XHRwYWRkaW5nLWxlZnQ6IHZhcigtLWNvbnRhaW5lci1wYWRkaW5nKTtcblx0fVxufVxuXG4uZ3JpZC1zZWN0aW9uX19jb250ZW50IHtcblx0Zm9udC1zaXplOiAyLjVyZW07XG5cdGxpbmUtaGVpZ2h0OiAxNDAlO1xufVxuXG4uY29udGFpbmVyLWxlZnQge1xuXHQvKiBvdmVyZmxvdy14OiBoaWRkZW47ICovXG5cdHBhZGRpbmctcmlnaHQ6IGNhbGMoXG5cdFx0KDEwMCUgLSB2YXIoLS1jb250YWluZXItd2lkdGgpKSAvIDIgKyB2YXIoLS1jb250YWluZXItcGFkZGluZylcblx0KTtcblxuXHRAbWVkaWEgKG1heC13aWR0aDogdmFyKC0tbGFwdG9wLXNpemUpKSB7XG5cdFx0cGFkZGluZy1yaWdodDogdmFyKC0tY29udGFpbmVyLXBhZGRpbmcpO1xuXHR9XG59XG5cbi5tdC1hdXRvIHtcbiAgICBtYXJnaW4tdG9wOiBhdXRvXG59XG5cbi5tdC01MCB7XG5cdG1hcmdpbi10b3A6NXJlbTtcbn1cbi5wdC01MCB7XG5cdHBhZGRpbmctdG9wOjVyZW07XG5cdEBtZWRpYSAobWF4LXdpZHRoOiA5OTJweCkge1xuXHRcdHBhZGRpbmctYm90dG9tOiA0cmVtO1xuXHR9XG5cdEBtZWRpYSAobWF4LXdpZHRoOiA3NjdweCkge1xuXHRcdHBhZGRpbmctYm90dG9tOiAycmVtO1xuXHR9XG5cdEBtZWRpYSAobWF4LXdpZHRoOiA1NjdweCkge1xuXHRcdHBhZGRpbmctYm90dG9tOiAxLjZyZW07XG5cdH1cbn1cbi5wYi01MCB7XG5cdHBhZGRpbmctYm90dG9tOjVyZW07XG5cdEBtZWRpYSAobWF4LXdpZHRoOiA5OTJweCkge1xuXHRcdHBhZGRpbmctYm90dG9tOiA0cmVtO1xuXHR9XG5cdEBtZWRpYSAobWF4LXdpZHRoOiA3NjdweCkge1xuXHRcdHBhZGRpbmctYm90dG9tOiAycmVtO1xuXHR9XG5cdEBtZWRpYSAobWF4LXdpZHRoOiA1NjdweCkge1xuXHRcdHBhZGRpbmctYm90dG9tOiAxLjZyZW07XG5cdH1cbn1cbi5wYi0zMCB7XG5cdHBhZGRpbmctYm90dG9tOjNyZW07XG5cdEBtZWRpYSAobWF4LXdpZHRoOiA5OTJweCkge1xuXHRcdHBhZGRpbmctYm90dG9tOiAzcmVtO1xuXHR9XG5cdEBtZWRpYSAobWF4LXdpZHRoOiA3NjdweCkge1xuXHRcdHBhZGRpbmctYm90dG9tOiAycmVtO1xuXHR9XG5cdEBtZWRpYSAobWF4LXdpZHRoOiA1NjdweCkge1xuXHRcdHBhZGRpbmctYm90dG9tOiAxLjZyZW07XG5cdH1cbn1cbi5nYXAtMyB7XG5cdGdhcDozcmVtO1xufVxuLm1iLTMge1xuXHRtYXJnaW4tYm90dG9tOiAzcmVtO1xuXG5cdEBtZWRpYSAobWF4LXdpZHRoOiA5OTJweCkge1xuXHRcdG1hcmdpbi1ib3R0b206IDJyZW07XG5cdH1cblx0QG1lZGlhIChtYXgtd2lkdGg6IDc2N3B4KSB7XG5cdFx0XG5cdH1cblx0QG1lZGlhIChtYXgtd2lkdGg6IDU2N3B4KSB7XG5cdFx0bWFyZ2luLWJvdHRvbTogMS42cmVtO1xuXHR9XG59XG5cbi5zcGFyZXMtYmxvY2sgdWwge1xuXHRtYXJnaW4tYm90dG9tOiAxcmVtO1xufVxuLnNwYXJlcy1ibG9jayB1bCBsaSB7XG5cdGxpc3Qtc3R5bGU6IGRpc2M7XG4gICAgbGlzdC1zdHlsZS1wb3NpdGlvbjogaW5zaWRlO1xuICAgIHBhZGRpbmctYm90dG9tOiAxcmVtO1xuICAgIGxpbmUtaGVpZ2h0OiAxNTAlO1xufVxuLnNwYXJlcy1ibG9jayB1bCBsaSBhIHt9XG5cbi5tdzc3MCB7XG5cdG1heC13aWR0aDogNzdyZW07XG5cblx0QG1lZGlhIChtYXgtd2lkdGg6OTkycHgpIHttYXgtd2lkdGg6IDEwMCU7fVxuXHRAbWVkaWEgKG1heC13aWR0aDo3NjdweCkge31cbn1cbi5tLTAtYXV0byB7XG5cdG1hcmdpbjogMCBhdXRvO1xufSIsImh0bWwsIGJvZHkge1xuICAgIG1pbi1oZWlnaHQ6IDEwMHZoO1xuICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbn1cblxuLmZvb3RlciB7XG4gICAgbWFyZ2luLXRvcDogYXV0bztcbn1cblxuLmZvb3RlciB7XG5cdHBhZGRpbmc6IDYwcHggMDtcblx0YmFja2dyb3VuZC1jb2xvcjogI2UzZTNlMztcbn0iLCIuZm9vdGVyIHtcblx0YmFja2dyb3VuZC1jb2xvcjogcmdiKDM5LCAzOSwgMzkpO1xuXHRwYWRkaW5nOiA1MHB4IDA7XG5cdGZvbnQtc2l6ZTogMzJweDtcbiAgICBjb2xvcjogI2ZmZjtcblxuXHRoMSB7XG5cdFx0Zm9udC1zaXplOiAzMnB4O1xuXHR9XG5cblx0YSB7XG5cdFx0Ly8gY29sb3I6IHZhcigtLWxpbmstY29sb3IpO1xuXHRcdGNvbG9yOiAjZmZmO1xuXHRcdHRleHQtZGVjb3JhdGlvbjogdW5kZXJsaW5lO1xuXHR9XG5cblx0QG1lZGlhIChtYXgtd2lkdGg6IDEyMDBweCkge1xuXHRcdGZvbnQtc2l6ZTogMjZweDtcblx0fVxufVxuXG4uZm9vdGVyX19jb3B5cmlnaHQge1xuXHRwYWRkaW5nOiAxMHB4IDA7XG5cdGZvbnQtc2l6ZTogMTZweDtcblxufVxuIiwiLmhlYWRlciB7XG59XG5cbi5oZWFkZXJfX25hdiB7XG5cdEBpbmNsdWRlIHRhYmxldCB7XG5cdFx0ZGlzcGxheTogbm9uZTtcblx0fVxufVxuLnN1Yi1oZWFkZXIge1xuXHRkaXNwbGF5OiBmbGV4O1xuXHRmbGV4LWRpcmVjdGlvbjogcm93O1xuXHRqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47XG5cdHRleHQtdHJhbnNmb3JtOiB1cHBlcmNhc2U7XG5cdGZvbnQtd2VpZ2h0OiB2YXIoLS1mb250LXdlaWdodC1zZW1pYm9sZCk7XG5cdGZvbnQtc2l6ZToxLjNyZW07XG5cdGNvbG9yOiB2YXIoLS10ZXh0LWNvbG9yKTtcblx0cGFkZGluZzogMXJlbSAwO1xuXHRib3JkZXItYm90dG9tOiAuMXJlbSBzb2xpZCB2YXIoLS1ib3JkZXJzLWNvbG9yKTtcblx0XG5cdCYgYSB7XG5cdGNvbG9yOiB2YXIoLS10ZXh0LWNvbG9yKTtcblx0fVxuXHQmIGE6aG92ZXIge1xuXHRcdGNvbG9yOiB2YXIoLS1hY2NlbnQtY29sb3IpO1xuXHR9XG5cdFxufVxuLnN1Yi1oZWFkZXJfX3NvY2lhbHMge1xufVxuLnN1Yi1oZWFkZXJfX2ljb24ge1xuXHRiYWNrZ3JvdW5kLWltYWdlOiB1cmwoJy4vLi4vaW1nL3N2Z2ljb25zL3doYXRzYXBwLnN2ZycpO1xuXHRiYWNrZ3JvdW5kLXNpemU6IDJyZW07XG5cdGJhY2tncm91bmQtcG9zaXRpb246IGxlZnQgY2VudGVyO1xuXHRiYWNrZ3JvdW5kLXJlcGVhdDogbm8tcmVwZWF0O1xuXHRwYWRkaW5nLWxlZnQ6IDNyZW07XG59XG4ucGhvbmUge1xuXHRmb250LXdlaWdodDogdmFyKC0tZm9udC13ZWlnaHQtYm9sZCk7XG59IiwiLmljb25zLXdyYXBwZXIge1xuXHRwYWRkaW5nOiAzMHB4IDA7XG5cdGRpc3BsYXk6IGZsZXg7XG5cdGNvbHVtbi1nYXA6IDMwcHg7XG59XG5cbi5pY29uIHtcblx0ZmlsbDogdHJhbnNwYXJlbnQ7XG5cdHN0cm9rZTogdHJhbnNwYXJlbnQ7XG5cdHdpZHRoOiA2MnB4O1xuXHRoZWlnaHQ6IDYycHg7XG59XG5cbi5pY29uLS1oZWFydC1saW5lIHtcblx0ZmlsbDogcmdiKDI0MSwgNjgsIDEzMSk7XG59XG5cbi5pY29uLS1pZC1jYXJkLWxpbmUge1xuXHRmaWxsOiByZ2IoNTEsIDUxLCA1MSk7XG59XG4uaWNvbi0tc2VhcmNoLWxpbmUge1xuXHRmaWxsOiByZ2IoMjgsIDE3NiwgODApO1xufVxuXG4uaWNvbi0tdXNlci1zdGFyIHtcblx0ZmlsbDogcmdiKDI2LCAxMzQsIDIzNSk7XG59XG5cbi5pY29uLS11c2VyIHtcblx0c3Ryb2tlOiByZ2IoMjYsIDEzNCwgMjM1KTtcblx0dHJhbnNpdGlvbjogYWxsIDAuMnMgZWFzZS1pbjtcblxuXHQmOmhvdmVyIHtcblx0XHRzdHJva2U6IHJnYigxNywgMTkzLCA5MCk7XG5cdH1cbn1cbiIsIi5sb2dvIHtcbiAgICBmb250LXNpemU6IDMycHg7XG59IiwiLm1vYmlsZS1uYXYge1xuXHRwb3NpdGlvbjogZml4ZWQ7XG5cdC8vIHRvcDogMDtcblx0dG9wOiAtMTAwJTtcblx0d2lkdGg6IDEwMCU7XG5cdGhlaWdodDogMTAwJTtcbiAgICB6LWluZGV4OiA5OTtcblxuXHRkaXNwbGF5OiBmbGV4O1xuICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcblx0anVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gICAgcGFkZGluZy10b3A6IDQwcHg7XG4gICAgcGFkZGluZy1ib3R0b206IDQwcHg7XG4gICAgLy8gYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDIwOWRlZywgIzQyODdiMyAtMTcuMzglLCAjMTMzZjZiIDc4LjQlKSwgIzI3NjE5NTtcblx0YmFja2dyb3VuZDogIzhjY2FlNjtcblx0dHJhbnNpdGlvbjogYWxsIDAuMnMgZWFzZS1pbjtcbn1cblxuLm1vYmlsZS1uYXYtLW9wZW4ge1xuXHR0b3A6IDA7XG59XG5cbi5tb2JpbGUtbmF2IGEge1xuXHRjb2xvcjogI2ZmZjtcbn1cblxuLm1vYmlsZS1uYXZfX2xpc3Qge1xuXHRkaXNwbGF5OiBmbGV4O1xuXHRmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuXHRhbGlnbi1pdGVtczogY2VudGVyO1xuXHRyb3ctZ2FwOiAyMHB4O1xuXHRmb250LXNpemU6IDI4cHg7XG5cblx0JiAuYWN0aXZlIHtcblx0XHRvcGFjaXR5OiAwLjU7XG5cdH1cbn0iLCIvKiBOYXYgSWNvbiAqL1xuLm1vYmlsZS1uYXYtYnRuIHtcblx0LS10aW1lOiAwLjFzO1xuXG5cdC0td2lkdGg6IDQwcHg7XG5cdC0taGVpZ2h0OiAzMHB4O1xuXG5cdC0tbGluZS1oZWlnaHQ6IDRweDtcblx0LS1zcGFjaW5nOiA2cHg7XG5cblx0LS1jb2xvcjogIzAwMDtcblx0LS1yYWRpdXM6IDRweDtcblxuXHQvKiBGaXhlZCBoZWlnaHQgYW5kIHdpZHRoICovXG5cdC8qIGhlaWdodDogdmFyKC0taGVpZ2h0KTsgKi9cblx0Lyogd2lkdGg6IHZhcigtLXdpZHRoKTsgKi9cblxuXHQvKiBEeW5hbWljIGhlaWdodCBhbmQgd2lkdGggKi9cblx0aGVpZ2h0OiBjYWxjKHZhcigtLWxpbmUtaGVpZ2h0KSAqIDMgKyB2YXIoLS1zcGFjaW5nKSAqIDIpO1xuXHR3aWR0aDogdmFyKC0td2lkdGgpO1xuXG5cdGRpc3BsYXk6IGZsZXg7XG5cdGp1c3RpZnktY29udGVudDogY2VudGVyO1xuXHRhbGlnbi1pdGVtczogY2VudGVyO1xufVxuXG4ubmF2LWljb24ge1xuXHRwb3NpdGlvbjogcmVsYXRpdmU7XG5cdHdpZHRoOiB2YXIoLS13aWR0aCk7XG5cdGhlaWdodDogdmFyKC0tbGluZS1oZWlnaHQpO1xuXHRiYWNrZ3JvdW5kLWNvbG9yOiB2YXIoLS1jb2xvcik7XG5cdGJvcmRlci1yYWRpdXM6IHZhcigtLXJhZGl1cyk7XG59XG5cbi5uYXYtaWNvbjo6YmVmb3JlLFxuLm5hdi1pY29uOjphZnRlciB7XG5cdGNvbnRlbnQ6ICcnO1xuXHRkaXNwbGF5OiBibG9jaztcblxuXHRwb3NpdGlvbjogYWJzb2x1dGU7XG5cdGxlZnQ6IDA7XG5cblx0d2lkdGg6IHZhcigtLXdpZHRoKTtcblx0aGVpZ2h0OiB2YXIoLS1saW5lLWhlaWdodCk7XG5cblx0Ym9yZGVyLXJhZGl1czogdmFyKC0tcmFkaXVzKTtcblx0YmFja2dyb3VuZC1jb2xvcjogdmFyKC0tY29sb3IpO1xuXHR0cmFuc2l0aW9uOiB0cmFuc2Zvcm0gdmFyKC0tdGltZSkgZWFzZS1pbixcblx0XHR0b3AgdmFyKC0tdGltZSkgbGluZWFyIHZhcigtLXRpbWUpO1xufVxuXG4ubmF2LWljb246OmJlZm9yZSB7XG5cdC8qIHRvcDogY2FsYyh2YXIoLS1saW5lLWhlaWdodCkgKiAtMik7ICovXG5cdHRvcDogY2FsYygtMSAqICh2YXIoLS1saW5lLWhlaWdodCkgKyB2YXIoLS1zcGFjaW5nKSkpO1xufVxuXG4ubmF2LWljb246OmFmdGVyIHtcblx0LyogdG9wOiBjYWxjKHZhcigtLWxpbmUtaGVpZ2h0KSAqIDIpOyAqL1xuXHR0b3A6IGNhbGModmFyKC0tbGluZS1oZWlnaHQpICsgdmFyKC0tc3BhY2luZykpO1xufVxuXG4ubmF2LWljb24ubmF2LWljb24tLWFjdGl2ZSB7XG5cdGJhY2tncm91bmQtY29sb3I6IHRyYW5zcGFyZW50O1xufVxuXG4ubmF2LWljb24ubmF2LWljb24tLWFjdGl2ZTo6YmVmb3JlLFxuLm5hdi1pY29uLm5hdi1pY29uLS1hY3RpdmU6OmFmdGVyIHtcblx0dG9wOiAwO1xuXHR0cmFuc2l0aW9uOiB0b3AgdmFyKC0tdGltZSkgbGluZWFyLFxuXHRcdHRyYW5zZm9ybSB2YXIoLS10aW1lKSBlYXNlLWluIHZhcigtLXRpbWUpO1xufVxuXG4ubmF2LWljb24ubmF2LWljb24tLWFjdGl2ZTo6YmVmb3JlIHtcblx0dHJhbnNmb3JtOiByb3RhdGUoNDVkZWcpO1xufVxuXG4ubmF2LWljb24ubmF2LWljb24tLWFjdGl2ZTo6YWZ0ZXIge1xuXHR0cmFuc2Zvcm06IHJvdGF0ZSgtNDVkZWcpO1xufVxuXG4vKiBMYXlvdXQgKi9cblxuLm1vYmlsZS1uYXYtYnRuIHtcblx0ei1pbmRleDogOTk5O1xuXHQvLyBkaXNwbGF5OiBub25lO1xufVxuXG4vLyBAbWVkaWEgKG1heC13aWR0aDogMTAwMHB4KSB7XG4vLyBcdC5tb2JpbGUtbmF2LWJ0biB7XG4vLyBcdFx0ZGlzcGxheTogYmxvY2s7XG4vLyBcdH1cbi8vIH0iLCIuaGVhZGVyX19uYXYge1xufVxuLm5hdiB7XG5cdGZvbnQtc2l6ZTogMThweDtcbn1cbi5uYXZfX2xpc3Qge1xuXHRkaXNwbGF5OiBmbGV4O1xuXHRjb2x1bW4tZ2FwOiAzMHB4O1xufVxuLmFjdGl2ZSB7XG59IiwiLnRpdGxlLTEge1xuXHRtYXJnaW46IDFlbSAwIDAuNWVtO1xuXHRmb250LXNpemU6IDM4cHg7XG5cdGZvbnQtd2VpZ2h0OiA3MDA7XG5cdGZvbnQtZmFtaWx5OiB2YXIoLS1mb250LXRpdGxlcyk7XG59XG5cbi50aXRsZS0yIHtcblx0bWFyZ2luOiAxZW0gMCAwLjVlbTtcblx0Zm9udC1zaXplOiAzMnB4O1xuXHRmb250LXdlaWdodDogNzAwO1xuXHRmb250LWZhbWlseTogdmFyKC0tZm9udC10aXRsZXMpO1xufVxuXG4udGl0bGUtMyB7XG5cdG1hcmdpbjogMWVtIDAgMC41ZW07XG5cdGZvbnQtc2l6ZTogMjZweDtcblx0Zm9udC13ZWlnaHQ6IDcwMDtcblx0Zm9udC1mYW1pbHk6IHZhcigtLWZvbnQtdGl0bGVzKTtcbn1cblxuLnRpdGxlLTQge1xuXHRtYXJnaW46IDFlbSAwIDAuNWVtO1xuXHRmb250LXNpemU6IDE4cHg7XG5cdGZvbnQtd2VpZ2h0OiA3MDA7XG5cdGZvbnQtZmFtaWx5OiB2YXIoLS1mb250LXRpdGxlcyk7XG59Il19 */
